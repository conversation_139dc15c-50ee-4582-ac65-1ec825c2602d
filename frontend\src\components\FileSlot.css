/* 文件槽位样式 - 复用web_frontend/index.html中的样式 */
.file-slot {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
}

.file-slot-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.file-item {
  position: relative;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.file-item:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.file-item img {
  max-width: 100%;
  max-height: 150px;
  object-fit: contain;
  border-radius: 6px;
  margin-bottom: 8px;
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.file-icon-placeholder {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  margin-bottom: 8px;
}

.file-icon {
  width: 48px;
  height: 48px;
  color: rgba(255, 255, 255, 0.6);
}

.file-name {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  word-break: break-all;
  line-height: 1.2;
  margin-bottom: 4px;
}

.file-meta {
  display: flex;
  gap: 8px;
  font-size: 9px;
  color: rgba(255, 255, 255, 0.6);
}

.file-size, .file-type {
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

.file-remove {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.file-remove:hover {
  background: rgba(255, 0, 0, 1);
  transform: scale(1.1);
}

.file-remove:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.file-slot-empty {
  background: rgba(255, 255, 255, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-slot-empty:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
}

.file-slot-empty.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.file-slot-empty.disabled:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.add-icon {
  font-size: 36px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 10px;
  font-weight: bold;
}

.add-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-slot {
    max-width: none;
    min-width: auto;
  }

  .file-item img {
    max-height: 120px;
  }

  .file-slot-empty {
    height: 100px;
  }

  .add-icon {
    font-size: 28px;
  }

  .add-text {
    font-size: 11px;
  }
}
