import React, { useState, useEffect, useRef } from 'react'
import { Check, X, Loader2, Edit3 } from 'lucide-react'
import { useApi } from '../contexts/ApiContext'
import { useMessage } from '../contexts/MessageContext'
import './StatusBar.css'

const StatusBar = () => {
  const { apiBase, connectionStatus, isConnected, checkConnection, updateApiBase } = useApi()
  const { showSuccess, showError } = useMessage()
  const [isEditing, setIsEditing] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const inputRef = useRef(null)

  // 初始化时和API地址变化时检查连接
  useEffect(() => {
    checkConnection()
  }, [apiBase, checkConnection])

  // 设置全局showMessage函数
  useEffect(() => {
    window.showMessage = (message, type) => {
      if (type === 'success') {
        showSuccess(message)
      } else if (type === 'error') {
        showError(message)
      }
    }
  }, [showSuccess, showError])

  // 格式化状态文本
  const formatStatusText = () => {
    const baseUrl = apiBase.replace('/api', '')
    switch (connectionStatus) {
      case 'connecting':
        return `正在连接 ${baseUrl}`
      case 'connected':
        return `已连接到 ${baseUrl}`
      case 'failed':
        return `连接失败 ${baseUrl}`
      default:
        return '连接中...'
    }
  }

  // 获取状态点样式
  const getStatusDotClass = () => {
    const baseClass = 'status-dot'
    switch (connectionStatus) {
      case 'connected':
        return `${baseClass} connected`
      case 'failed':
        return `${baseClass} failed`
      default:
        return `${baseClass} connecting`
    }
  }

  // 进入编辑模式
  const enterEditMode = () => {
    if (isEditing) return
    
    setIsEditing(true)
    const baseUrl = apiBase.replace('/api', '')
    setInputValue(inputValue || baseUrl)
    
    // 延迟聚焦，确保输入框已渲染
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus()
        inputRef.current.select()
      }
    }, 100)
  }

  // 退出编辑模式
  const exitEditMode = (save = false) => {
    if (!isEditing) return
    
    if (save) {
      handleSave()
    } else {
      setIsEditing(false)
      setInputValue('')
    }
  }

  // 保存更改
  const handleSave = async () => {
    if (!inputValue.trim()) {
      showError('地址不能为空')
      return
    }

    setIsLoading(true)
    
    try {
      const result = await updateApiBase(inputValue.trim())
      
      if (result.success) {
        showSuccess('连接地址更新成功')
        setIsEditing(false)
        setInputValue('')
      } else {
        showError(`连接失败: ${result.message}`)
        // 保持编辑模式，让用户可以继续修改
      }
    } catch (error) {
      showError(`更新失败: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理键盘事件
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      exitEditMode(true)
    } else if (e.key === 'Escape') {
      e.preventDefault()
      exitEditMode(false)
    }
  }

  // 处理输入变化
  const handleInputChange = (e) => {
    setInputValue(e.target.value)
  }

  // 点击状态栏
  const handleStatusClick = () => {
    if (!isEditing) {
      enterEditMode()
    }
  }

  return (
    <div className={`status-bar ${isEditing ? 'editing' : ''}`}>
      <div className="status-indicator" onClick={handleStatusClick}>
        <div className={getStatusDotClass()}>
          {connectionStatus === 'connecting' && <Loader2 className="status-icon spinning" />}
          {connectionStatus === 'connected' && <Check className="status-icon" />}
          {connectionStatus === 'failed' && <X className="status-icon" />}
        </div>
        
        <div className="status-content">
          {!isEditing ? (
            <span className="status-text">
              {formatStatusText()}
            </span>
          ) : (
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              className="status-input"
              placeholder="输入后端地址..."
              disabled={isLoading}
            />
          )}
        </div>

        {!isEditing && (
          <Edit3 className="edit-icon" />
        )}
      </div>

      {isEditing && (
        <div className="status-actions">
          <button
            className="status-btn confirm"
            onClick={() => exitEditMode(true)}
            disabled={isLoading}
            title="确认"
          >
            {isLoading ? <Loader2 className="btn-icon spinning" /> : <Check className="btn-icon" />}
          </button>
          <button
            className="status-btn cancel"
            onClick={() => exitEditMode(false)}
            disabled={isLoading}
            title="取消"
          >
            <X className="btn-icon" />
          </button>
        </div>
      )}
    </div>
  )
}

export default StatusBar
