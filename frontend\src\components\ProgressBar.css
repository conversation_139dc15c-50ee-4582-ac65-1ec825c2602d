.progress-container {
  text-align: center;
}

.progress-container.small {
  margin: 8px 0;
}

.progress-container.medium {
  margin: 12px 0;
}

.progress-container.large {
  margin: 16px 0;
}

.progress-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: white;
}

.progress-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-primary);
  text-align: left;
}

.progress-bar-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  border: 1px solid var(--border-color);
}

.progress-container.small .progress-bar {
  height: 6px;
}

.progress-container.medium .progress-bar {
  height: 8px;
}

.progress-container.large .progress-bar {
  height: 12px;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill.animated {
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 不同状态的颜色 */
.progress-processing .progress-fill {
  background: linear-gradient(45deg, #3b82f6 0%, #1d4ed8 100%);
}

.progress-success .progress-fill {
  background: linear-gradient(45deg, #10b981 0%, #059669 100%);
}

.progress-error .progress-fill {
  background: linear-gradient(45deg, #ef4444 0%, #dc2626 100%);
}

.progress-warning .progress-fill {
  background: linear-gradient(45deg, #f59e0b 0%, #d97706 100%);
}

/* 动画效果 */
.progress-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.progress-percentage {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  min-width: 40px;
  text-align: right;
}

.progress-text {
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .progress-title {
    font-size: 16px;
  }

  .progress-text {
    font-size: 14px;
  }

  .progress-percentage {
    font-size: 11px;
    min-width: 35px;
  }

  .progress-bar-wrapper {
    gap: 8px;
  }
}
