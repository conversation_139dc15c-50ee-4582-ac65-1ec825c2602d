"""
任务处理路由
"""
import os
import logging
from flask import Blueprint, request, jsonify

from config import UPLOAD_FOLDER
from utils.validation import validate_file_path, validate_parameters

logger = logging.getLogger(__name__)

process_bp = Blueprint('process', __name__)

# 内存中的批处理存储（生产环境应使用Redis）
batch_storage = {}


@process_bp.route('/api/process', methods=['POST'])
def process_files():
    """文件处理接口"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        files = data.get('files', [])
        operation = data.get('operation')
        parameters = data.get('parameters', {})

        if not files or not operation:
            return jsonify({'error': 'Missing files or operation'}), 400

        # 验证操作参数
        is_valid, message = validate_parameters(operation, parameters)
        if not is_valid:
            return jsonify({'error': f'Invalid parameters: {message}'}), 400

        task_ids = []
        failed_files = []

        # 处理双文件操作（如图像融合、纹理迁移）
        if operation in ['image_fusion', 'texture_transfer']:
            if len(files) % 2 != 0:
                return jsonify({'error': f'Operation {operation} requires pairs of files'}), 400

            for i in range(0, len(files), 2):
                file1_info = files[i]
                file2_info = files[i + 1] if i + 1 < len(files) else None

                if not file2_info:
                    continue

                # 从stored_filename构造正确的文件路径
                file1_stored = file1_info.get('stored_filename')
                file2_stored = file2_info.get('stored_filename')
                
                if not file1_stored or not file2_stored:
                    continue

                file1_path = os.path.abspath(os.path.join(UPLOAD_FOLDER, file1_stored))
                file2_path = os.path.abspath(os.path.join(UPLOAD_FOLDER, file2_stored))

                # 验证两个文件路径
                abs_upload_folder = os.path.abspath(UPLOAD_FOLDER)
                if (not validate_file_path(file1_path, abs_upload_folder) or
                    not validate_file_path(file2_path, abs_upload_folder) or
                    not os.path.exists(file1_path) or not os.path.exists(file2_path)):
                    continue

                try:
                    task = None
                    # 从app中获取已注册的任务
                    from app import tasks

                    if operation == 'image_fusion':
                        task = tasks['image_fusion'].delay(file1_path, file2_path, **parameters)
                    elif operation == 'texture_transfer':
                        task = tasks['texture_transfer'].delay(file1_path, file2_path, parameters)

                    if task:
                        task_ids.append({
                            'task_id': task.id,
                            'file1': file1_info.get('filename', 'unknown'),
                            'file2': file2_info.get('filename', 'unknown'),
                            'operation': operation
                        })
                except Exception as e:
                    logger.error(f"Failed to process files {file1_path}, {file2_path}: {str(e)}")
                    failed_files.append({
                        'file1': file1_info.get('filename', 'unknown'),
                        'file2': file2_info.get('filename', 'unknown'),
                        'error': str(e)
                    })
        elif operation in ['image_stitching']:
            # 处理多文件操作（图像拼接）
            if len(files) < 2:
                return jsonify({'error': f'Operation {operation} requires at least 2 files'}), 400

            # 收集所有文件路径
            file_paths = []
            for file_info in files:
                stored_filename = file_info.get('stored_filename')
                if not stored_filename:
                    continue

                file_path = os.path.abspath(os.path.join(UPLOAD_FOLDER, stored_filename))

                # 验证文件路径
                if not validate_file_path(file_path, os.path.abspath(UPLOAD_FOLDER)):
                    continue
                if not os.path.exists(file_path):
                    continue

                file_paths.append(file_path)

            if len(file_paths) < 2:
                return jsonify({'error': 'Not enough valid files for stitching'}), 400

            try:
                # 从app中获取已注册的任务
                from app import tasks
                task = tasks['image_stitching'].delay(file_paths, parameters)

                if task:
                    task_ids.append({
                        'task_id': task.id,
                        'files': [f.get('filename', 'unknown') for f in files],
                        'operation': operation
                    })
            except Exception as e:
                logger.error(f"Failed to process stitching files: {str(e)}")
                failed_files.append({
                    'files': [f.get('filename', 'unknown') for f in files],
                    'error': str(e)
                })
        else:
            # 处理单文件操作
            for file_info in files:
                logger.info(f"Processing file_info: {file_info}")
                
                # 从file_info中获取stored_filename并构造完整路径
                stored_filename = file_info.get('stored_filename')
                if not stored_filename:
                    logger.warning(f"No stored_filename in file_info: {file_info}")
                    continue
                
                # 构造正确的绝对文件路径
                file_path = os.path.join(UPLOAD_FOLDER, stored_filename)
                file_path = os.path.abspath(file_path)  # 确保为绝对路径
                
                logger.info(f"Constructed file_path: {file_path}")
                
                # 验证文件路径
                if not validate_file_path(file_path, os.path.abspath(UPLOAD_FOLDER)):
                    logger.warning(f"File path validation failed: {file_path}, UPLOAD_FOLDER: {os.path.abspath(UPLOAD_FOLDER)}")
                    continue

                if not os.path.exists(file_path):
                    logger.warning(f"File does not exist: {file_path}")
                    continue

                logger.info(f"File validated successfully: {file_path}")

                try:
                    task = None
                    # 从app中获取已注册的任务
                    from app import tasks
                    
                    if tasks is None:
                        logger.error("Tasks not registered, skipping file processing")
                        failed_files.append({
                            'filename': file_info.get('filename', 'unknown'),
                            'error': 'Tasks not registered'
                        })
                        continue
                    
                    logger.info(f"Available tasks: {list(tasks.keys()) if tasks else 'None'}")
                    
                    # 根据操作类型动态选择任务
                    # 图像专用操作
                    if operation == 'sharpen':
                        task = tasks['sharpen_image'].delay(file_path, parameters)
                    elif operation == 'gamma_correction':
                        task = tasks['gamma_correction'].delay(file_path, parameters)
                    elif operation == 'grayscale':
                        task = tasks['grayscale_image'].delay(file_path, parameters)
                    elif operation == 'canny_edge':
                        task = tasks['canny_edge_detection'].delay(file_path, parameters)
                    elif operation == 'beauty_enhancement':
                        task = tasks['beauty_enhancement'].delay(file_path, parameters)
                    # 视频操作（不包括grayscale，因为图像灰度转换有专门的任务）
                    elif operation in ['resize', 'binary', 'blur', 'edge_detection', 'transform', 'extract_frame']:
                        # 所有视频操作都通过process_video_task处理
                        task = tasks['process_video_task'].delay(
                            file_path=file_path,
                            operation=operation,
                            parameters=parameters
                        )

                    if task:
                        task_ids.append({
                            'task_id': task.id,
                            'filename': file_info.get('filename', 'unknown'),
                            'operation': operation
                        })
                except Exception as e:
                    logger.error(f"Failed to process file {file_path}: {str(e)}")
                    failed_files.append({
                        'filename': file_info.get('filename', 'unknown'),
                        'error': str(e)
                    })

        if not task_ids:
            return jsonify({'error': 'No tasks were submitted successfully'}), 400

        # 生成批处理ID
        import uuid
        batch_id = str(uuid.uuid4())

        # 存储批处理信息
        batch_storage[batch_id] = {
            'task_ids': [task['task_id'] for task in task_ids],
            'operation': operation,
            'total_tasks': len(task_ids),
            'failed_files': failed_files
        }

        return jsonify({
            'batch_id': batch_id,
            'task_ids': task_ids,
            'total_tasks': len(task_ids),
            'failed_files': failed_files,
            'message': f'{operation} submitted successfully'
        })

    except Exception as e:
        logger.error(f"Processing error: {str(e)}")
        return jsonify({'error': 'Processing failed'}), 500


@process_bp.route('/api/process/status', methods=['POST'])
def get_process_status():
    """查询任务状态"""
    data = request.get_json()
    if not data or 'task_ids' not in data:
        return jsonify({'error': 'No task IDs provided'}), 400

    task_ids = data['task_ids']
    if not isinstance(task_ids, list):
        return jsonify({'error': 'task_ids must be a list'}), 400

    try:

        # 导入celery实例
        from app import celery

        results = []
        for task_id in task_ids:
            try:
                task = celery.AsyncResult(task_id)
                status = task.status
                result = None
                progress = 0

                if status == 'SUCCESS':
                    result = task.result
                    progress = 100
                elif status == 'FAILURE':
                    result = str(task.info)
                    progress = 0
                elif status == 'PENDING':
                    progress = 0
                elif status == 'PROGRESS':
                    if hasattr(task.info, 'get'):
                        progress = task.info.get('progress', 0)
                    else:
                        progress = 50

                results.append({
                    'task_id': task_id,
                    'status': status,
                    'result': result,
                    'progress': progress
                })
            except Exception as e:
                logger.error(f"Error getting status for task {task_id}: {str(e)}")
                results.append({
                    'task_id': task_id,
                    'status': 'ERROR',
                    'result': f'Status query error: {str(e)}',
                    'progress': 0
                })

        return jsonify({
            'results': results,
            'total_tasks': len(task_ids)
        })

    except Exception as e:
        logger.error(f"Status query error: {str(e)}")
        return jsonify({'error': 'Failed to query status'}), 500


def _check_task_completion_by_files(task_id):
    """通过检查输出文件来确定任务是否完成"""
    import os
    import glob
    import time
    import logging

    logger = logging.getLogger(__name__)

    output_dir = 'output'
    if not os.path.exists(output_dir):
        logger.warning(f"Output directory does not exist: {output_dir}")
        return False, None

    # 首先尝试根据任务ID查找对应的输出文件
    # 输出文件命名格式通常为: {operation}_{file_id}_{task_id}.{ext}
    task_pattern = os.path.join(output_dir, f'*_{task_id}.*')

    logger.info(f"Searching for task {task_id} with pattern: {task_pattern}")

    # 添加重试机制处理文件系统时序问题
    max_retries = 3
    retry_delay = 0.1  # 100ms

    for attempt in range(max_retries):
        task_files = glob.glob(task_pattern)
        logger.info(f"Attempt {attempt + 1}: Found task-specific files: {task_files}")

        if task_files:
            # 找到了包含任务ID的文件，返回最新的一个（如果有多个）
            task_files.sort(key=os.path.getmtime, reverse=True)
            matched_file = task_files[0]
            logger.info(f"Task {task_id} matched file: {matched_file}")
            return True, {
                'status': 'success',
                'output_path': matched_file,
                'output_filename': os.path.basename(matched_file),
                'note': f'Task completed - found output file for task {task_id}'
            }

        if attempt < max_retries - 1:
            logger.info(f"No files found for task {task_id}, retrying in {retry_delay}s...")
            time.sleep(retry_delay)

    logger.warning(f"No task-specific files found for {task_id} after {max_retries} attempts")

    # 如果没有找到包含任务ID的文件，作为回退方案查找最近的文件
    # 但这种情况应该很少发生，主要用于兼容性
    pattern = os.path.join(output_dir, '*')
    all_files = glob.glob(pattern)
    logger.info(f"No task-specific file found for {task_id}, checking recent files")

    # 查找最近5分钟内创建的文件
    recent_threshold = time.time() - 300  # 5分钟
    recent_files = [f for f in all_files if os.path.getmtime(f) > recent_threshold]
    logger.info(f"Recent files (last 5 minutes): {recent_files}")

    if recent_files:
        # 按修改时间排序，获取最新的文件
        recent_files.sort(key=os.path.getmtime, reverse=True)
        latest_file = recent_files[0]
        logger.warning(f"Task {task_id} using fallback file: {latest_file}")
        return True, {
            'status': 'success',
            'output_path': latest_file,
            'output_filename': os.path.basename(latest_file),
            'note': f'Task completed - fallback to latest file (no task-specific file found for {task_id})'
        }

    logger.error(f"No files found for task {task_id}")
    return False, None


@process_bp.route('/api/process/results', methods=['POST'])
def get_process_results():
    """获取处理结果 - 简化版本"""
    data = request.get_json()
    if not data or 'task_ids' not in data:
        return jsonify({'error': 'No task IDs provided'}), 400

    task_ids = data['task_ids']
    if not isinstance(task_ids, list):
        return jsonify({'error': 'task_ids must be a list'}), 400

    try:

        # 导入celery实例
        from app import celery

        results = []
        for task_id in task_ids:
            try:
                task = celery.AsyncResult(task_id)

                # 优化状态查询：添加重试机制和更好的状态检查
                status = task.status
                result_data = None

                # 对于SUCCESS状态，尝试获取结果，如果失败则重试一次
                if status == 'SUCCESS':
                    try:
                        result_data = task.result
                        # 验证结果数据的完整性
                        if result_data and isinstance(result_data, dict) and 'output_filename' in result_data:
                            results.append({
                                'task_id': task_id,
                                'status': 'SUCCESS',
                                'result': result_data
                            })
                            continue
                        else:
                            logger.warning(f"Task {task_id} has SUCCESS status but invalid result data: {result_data}")
                    except Exception as e:
                        logger.warning(f"Failed to get result for {task_id} on first attempt: {str(e)}")

                        # 重试一次获取结果
                        try:
                            import time
                            time.sleep(0.1)  # 短暂等待
                            task = celery.AsyncResult(task_id)  # 重新获取任务对象
                            if task.status == 'SUCCESS':
                                result_data = task.result
                                if result_data and isinstance(result_data, dict) and 'output_filename' in result_data:
                                    results.append({
                                        'task_id': task_id,
                                        'status': 'SUCCESS',
                                        'result': result_data
                                    })
                                    continue
                        except Exception as retry_e:
                            logger.warning(f"Retry failed for {task_id}: {str(retry_e)}")

                # 如果SUCCESS状态但无法获取有效结果，或者状态不是SUCCESS，则检查文件
                # file_completed, file_result = _check_task_completion_by_files(task_id)
                # if file_completed:
                #     results.append({
                #         'task_id': task_id,
                #         'status': 'SUCCESS',
                #         'result': file_result
                #     })
                #     if status == 'SUCCESS':
                #         logger.info(f"Task {task_id} SUCCESS status but used file fallback")
                #     else:
                #         logger.info(f"Task {task_id} appears completed (found output file) but Celery status is {status}")
                #     continue

                # 处理失败状态
                if status in ['FAILURE', 'REVOKED', 'ERROR']:
                    try:
                        error_info = str(task.info) if task.info else 'Unknown error'
                    except:
                        error_info = 'Error details unavailable'

                    results.append({
                        'task_id': task_id,
                        'status': 'FAILURE',
                        'error': error_info
                    })
                else:
                    # 任务仍在处理中 (PENDING, PROGRESS, RETRY, STARTED, RECEIVED)
                    # 这种情况现在应该很少发生，因为上面已经检查了文件
                    results.append({
                        'task_id': task_id,
                        'status': 'PROCESSING',
                        'result': None
                    })

            except Exception as e:
                logger.error(f"Error processing task {task_id}: {str(e)}")
                results.append({
                    'task_id': task_id,
                    'status': 'ERROR',
                    'error': f'Task processing error: {str(e)}'
                })

        return jsonify({
            'results': results,
            'total_tasks': len(task_ids)
        })

    except Exception as e:
        logger.error(f"Results query error: {str(e)}")
        return jsonify({'error': 'Failed to get results'}), 500
