# 文件处理器 - React版本

这是使用React框架重构的文件处理器前端应用。

## 🚀 特性

- **现代化架构**: 使用React 18 + Vite构建
- **组件化设计**: 清晰的组件分离和职责划分
- **状态管理**: 使用React Context进行状态管理
- **响应式设计**: 适配各种屏幕尺寸
- **丝滑动画**: 流畅的用户交互体验
- **TypeScript支持**: 类型安全的开发体验

## 📁 项目结构

```
src/
├── components/          # React组件
│   ├── Header.jsx      # 头部组件
│   ├── StatusBar.jsx   # 状态栏组件
│   ├── FileUpload.jsx  # 文件上传组件
│   ├── OperationPanel.jsx # 操作面板组件
│   └── ...
├── contexts/           # React Context
│   ├── ApiContext.jsx  # API管理上下文
│   └── MessageContext.jsx # 消息管理上下文
├── hooks/              # 自定义Hooks
├── utils/              # 工具函数
├── App.jsx            # 主应用组件
├── main.jsx           # 应用入口
└── index.css          # 全局样式
```

## 🛠️ 安装和运行

### 前置要求

- Node.js 16+ 
- npm 或 yarn

### 安装依赖

```bash
cd react-app
npm install
```

### 开发模式

```bash
npm run dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

构建文件将输出到 `dist/` 目录

### 预览生产版本

```bash
npm run preview
```

## 🎯 主要组件

### StatusBar 组件

- **功能**: 显示后端连接状态，支持点击编辑API地址
- **特性**: 
  - 实时连接状态显示
  - 点击编辑API地址
  - 自动连接测试
  - 配置持久化
  - 丝滑动画效果

### ApiContext

- **功能**: 管理API配置和连接状态
- **提供**: 
  - API地址管理
  - 连接状态检查
  - 文件处理接口
  - 配置持久化

### MessageContext

- **功能**: 全局消息提示管理
- **提供**:
  - 成功/错误/信息提示
  - 自动消失机制
  - 多消息队列管理

## 🎨 设计特色

- **渐变背景**: 美观的蓝色渐变背景
- **毛玻璃效果**: 现代化的半透明卡片设计
- **流畅动画**: 基于CSS3的平滑过渡效果
- **响应式布局**: 适配桌面和移动设备
- **图标系统**: 使用Lucide React图标库

## 🔧 技术栈

- **React 18**: 现代化的React框架
- **Vite**: 快速的构建工具
- **Lucide React**: 美观的图标库
- **CSS3**: 现代化的样式和动画
- **Context API**: React原生状态管理

## 📱 响应式设计

- **桌面**: 1200px+ 双列布局
- **平板**: 768px-1199px 单列布局
- **手机**: <768px 紧凑布局

## 🚀 相比原版的优势

1. **组件化**: 清晰的组件分离，易于维护
2. **状态管理**: 统一的状态管理，避免prop drilling
3. **类型安全**: TypeScript支持，减少运行时错误
4. **开发体验**: 热重载，快速开发迭代
5. **构建优化**: Vite提供的快速构建和优化
6. **现代化**: 使用最新的React特性和最佳实践

## 🔄 迁移说明

这个React版本完全重构了原有的vanilla JavaScript代码：

- 将混乱的单文件代码拆分为清晰的组件
- 使用React Hooks管理组件状态
- 通过Context API实现全局状态管理
- 采用现代化的CSS-in-JS或CSS Modules
- 提供更好的开发和调试体验

## 🎯 下一步计划

- [ ] 添加更多文件处理组件
- [ ] 实现拖拽上传功能
- [ ] 添加进度条组件
- [ ] 实现结果展示组件
- [ ] 添加单元测试
- [ ] 优化性能和用户体验
