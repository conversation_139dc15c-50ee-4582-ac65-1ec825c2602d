/* 文件组管理器样式 */
.file-group-manager {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.header-info {
  flex: 1;
}

.manager-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: white;
  display: flex;
  align-items: center;
  gap: 10px;
}

.stats-info {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.stat-item {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  padding: 4px 8px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
}

.stat-item.complete {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.stat-item.incomplete {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-success {
  background: linear-gradient(45deg, #10b981 0%, #059669 100%);
  color: white;
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.7);
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 20px;
  color: rgba(255, 255, 255, 0.4);
}

.empty-state p {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.empty-description {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 20px;
}

.help-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 13px;
  color: rgba(245, 158, 11, 0.9);
  background: rgba(245, 158, 11, 0.1);
  padding: 10px 15px;
  border-radius: 8px;
  border: 1px solid rgba(245, 158, 11, 0.2);
  max-width: 400px;
  margin: 0 auto;
}

.help-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.file-groups-list {
  margin-bottom: 20px;
}

.manager-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.warning-message {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: rgba(245, 158, 11, 0.9);
  background: rgba(245, 158, 11, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.warning-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-group-manager {
    padding: 15px;
  }

  .manager-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .stats-info {
    justify-content: center;
  }

  .btn {
    justify-content: center;
  }

  .empty-state {
    padding: 30px 15px;
  }

  .empty-icon {
    width: 48px;
    height: 48px;
  }

  .help-text {
    flex-direction: column;
    text-align: center;
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .file-group-manager {
    padding: 12px;
  }

  .manager-title {
    font-size: 18px;
  }

  .stats-info {
    gap: 10px;
  }

  .stat-item {
    font-size: 12px;
    padding: 3px 6px;
  }

  .btn {
    padding: 8px 12px;
    font-size: 13px;
  }

  .empty-state {
    padding: 25px 10px;
  }

  .empty-state p {
    font-size: 15px;
  }

  .empty-description {
    font-size: 13px;
  }
}
