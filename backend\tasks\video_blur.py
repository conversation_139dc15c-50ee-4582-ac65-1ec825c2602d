"""
视频滤波处理任务（模糊等效果）
"""
import os
import logging
from celery import current_task

from tasks.opencv_ffmpeg_processor import OpenCVFFmpegProcessor, create_opencv_processor

logger = logging.getLogger(__name__)


def create_celery_progress_callback(task_instance):
    """创建Celery进度回调函数"""
    def progress_callback(current, total, status="Processing"):
        task_instance.update_state(
            state='PROGRESS',
            meta={'current': current, 'total': total, 'status': status}
        )
    return progress_callback


def blur_video_task(self, file_path, parameters):
    """视频滤波处理（模糊）"""
    try:
        progress_callback = create_celery_progress_callback(self)
        
        # 获取滤波参数
        filter_type = parameters.get('filter_type', 'gaussian')
        kernel_size = parameters.get('kernel_size', 5)
        sigma = parameters.get('sigma', 1.0)
        
        # 创建滤波处理器
        opencv_processor = create_opencv_processor(
            'blur', 
            filter_type=filter_type, 
            kernel_size=kernel_size, 
            sigma=sigma
        )
        
        with OpenCVFFmpegProcessor(file_path, preserve_audio=True) as processor:
            result = processor.process_frames_with_opencv(
                opencv_processor=opencv_processor,
                output_prefix=f'{filter_type}_filtered',
                progress_callback=progress_callback
            )
            
            # 添加滤波参数到结果
            result.update({
                'filter_type': filter_type,
                'kernel_size': kernel_size,
                'sigma': sigma
            })
            
            self.update_state(state='SUCCESS', meta=result)
            return result
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Blur video task failed: {error_msg}")
        self.update_state(state='FAILURE', meta={'error': error_msg})
        raise Exception(f"Video blur processing failed: {error_msg}")