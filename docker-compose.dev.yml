services:
  # Redis 服务 - Celery 消息队列和结果存储（可选）
  # 如果系统已有Redis，可以不启动此服务
  redis:
    image: redis:7-alpine
    container_name: file-processor-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"  # 使用6380端口避免与系统Redis冲突
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_dev_data:
    driver: local
