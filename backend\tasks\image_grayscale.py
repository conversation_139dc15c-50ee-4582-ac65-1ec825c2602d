"""
图像灰度化任务
"""
import cv2
import os
import sys
import time
import logging
from celery import current_task

logger = logging.getLogger(__name__)


def grayscale_image_task(self, file_path, parameters):
    """灰度化处理"""
    try:
        self.update_state(state='PROGRESS',
                         meta={'current': 10, 'total': 100,
                               'status': 'Loading image...'})

        image = cv2.imread(file_path)
        if image is None:
            raise ValueError(f"Cannot load image from {file_path}")

        self.update_state(state='PROGRESS',
                         meta={'current': 50, 'total': 100,
                               'status': 'Converting to grayscale...'})

        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        self.update_state(state='PROGRESS',
                         meta={'current': 80, 'total': 100,
                               'status': 'Saving result...'})

        # 保存结果
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        # 生成唯一文件名避免缓存问题 - 使用任务ID确保唯一性
        task_id = self.request.id if hasattr(self, 'request') and self.request else 'unknown'
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        ext = os.path.splitext(os.path.basename(file_path))[1]
        output_filename = f'gray_{base_name}_{task_id}{ext}'
        output_path = os.path.join(output_dir, output_filename)
        cv2.imwrite(output_path, gray)

        result = {
            'status': 'success',
            'output_path': output_path,
            'output_filename': output_filename,
            'original_path': file_path
        }

        # 设置任务状态为成功
        self.update_state(state='SUCCESS', meta=result)
        return result

    except Exception as e:
        error_msg = str(e)
        logger.error(f"Grayscale conversion task failed: {error_msg}")
        raise Exception(f"Grayscale conversion failed: {error_msg}")