"""
视频帧提取任务
"""
import os
import cv2
import logging
import time
from celery import current_task

logger = logging.getLogger(__name__)


def create_celery_progress_callback(task_instance):
    """创建Celery进度回调函数"""
    def progress_callback(current, total, status="Processing"):
        task_instance.update_state(
            state='PROGRESS',
            meta={'current': current, 'total': total, 'status': status}
        )
    return progress_callback


def extract_video_frame_task(self, file_path, parameters):
    """从视频中提取指定帧"""
    try:
        progress_callback = create_celery_progress_callback(self)
        
        progress_callback(10, 100, 'Opening video file...')
        
        frame_number = parameters.get('frame_number', 0)
        
        # 使用OpenCV直接提取帧
        cap = cv2.VideoCapture(file_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {file_path}")
        
        try:
            # 获取视频总帧数
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            if frame_number >= total_frames:
                raise ValueError(f"Frame number {frame_number} exceeds total frames {total_frames}")
            
            progress_callback(30, 100, f'Seeking to frame {frame_number}...')
            
            # 跳转到指定帧
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            progress_callback(60, 100, 'Extracting frame...')
            
            # 读取帧
            ret, frame = cap.read()
            if not ret:
                raise ValueError(f"Cannot read frame {frame_number}")
            
            progress_callback(80, 100, 'Saving frame...')
            
            # 保存帧
            output_dir = 'output'
            os.makedirs(output_dir, exist_ok=True)
            
            timestamp = int(time.time() * 1000)
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_filename = f'frame_{frame_number}_{base_name}_{timestamp}.jpg'
            output_path = os.path.join(output_dir, output_filename)
            
            cv2.imwrite(output_path, frame)
            
            progress_callback(100, 100, 'Frame extraction completed')
            
            result = {
                'status': 'success',
                'output_path': output_path,
                'output_filename': output_filename,
                'original_path': file_path,
                'frame_number': frame_number,
                'total_frames': total_frames
            }
            
            self.update_state(state='SUCCESS', meta=result)
            return result
            
        finally:
            cap.release()
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Extract frame task failed: {error_msg}")
        self.update_state(state='FAILURE', meta={'error': error_msg})
        raise Exception(f"Video frame extraction failed: {error_msg}")