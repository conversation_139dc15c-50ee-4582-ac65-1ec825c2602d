import React from 'react'
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'
import { useMessage } from '../contexts/MessageContext'
import './MessageDisplay.css'

const MessageDisplay = () => {
  const { messages, removeMessage } = useMessage()

  const getIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="message-icon" />
      case 'error':
        return <AlertCircle className="message-icon" />
      case 'warning':
        return <AlertTriangle className="message-icon" />
      default:
        return <Info className="message-icon" />
    }
  }

  if (messages.length === 0) {
    return null
  }

  return (
    <div className="message-container">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`message message-${message.type}`}
        >
          {getIcon(message.type)}
          <span className="message-text">{message.text}</span>
          <button
            className="message-close"
            onClick={() => removeMessage(message.id)}
          >
            <X className="close-icon" />
          </button>
        </div>
      ))}
    </div>
  )
}

export default MessageDisplay
