下面给出 **“可落地、可按阶段执行”** 的重构方案。  
每一点都对应到你上传的 `BatchProcessor.jsx` 与 `BatchResults.jsx` 中出现的具体问题，并给出 **新文件结构、推荐技术选型、代码示例片段、渐进式迁移步骤**。

---

## 0. 目录与基线改造（所有后续工作的地基）

```
src/
├─ components/          // 纯展示组件（无副作用）
│  ├─ batch/
│  │  ├─ BatchProcessorPage.tsx
│  │  ├─ TaskCard.tsx
│  │  ├─ FileSection.tsx
│  │  ├─ ParameterGrid.tsx
│  │  └─ …
│  └─ results/
│     ├─ ResultsModal.tsx
│     ├─ ProgressSummary.tsx
│     └─ ResultCard.tsx
├─ hooks/
│  ├─ useOperations.ts
│  ├─ useBatchProcessor.ts
│  └─ useBatchResults.ts
├─ services/
│  ├─ fileService.ts
│  ├─ batchService.ts
│  └─ operationsService.ts
├─ utils/
│  ├─ fileUtils.ts
│  ├─ statusMap.ts
│  └─ download.ts
├─ config/
│  └─ parameterConfig.ts         // 由后端或 JSON 生成
└─ store/                        // Zustand 或 useReducer
    └─ batchStore.ts
```

- **统一 TypeScript**：先 `npx tsc --init`，再逐步把 `*.jsx` ⇒ `*.tsx`。  
- **代码规范**：ESLint + Prettier + Git hooks（lint‑staged）。

---

## 1. 抽出「参数元数据」与「操作表」

### 问题定位  
`getParameterConfig` 在 `BatchProcessor.jsx` 出现大量重复字段，且与 UI 耦合严重 。

### 方案  
1. 在 `config/parameterConfig.ts` 中集中维护：
   ```ts
   export interface ParameterMeta {  
     name: string  
     type: 'slider' | 'select' | 'checkbox' | 'number' | 'video_frame_picker'  
     default: number | string | boolean  
     label: string  
     description?: string  
     options?: { value: string; label: string }[]  
     dependsOn?: string  
     showWhen?: string | number | boolean  
     min?: number  
     max?: number  
     step?: number  
   }

   export const parameterConfig: Record<string, ParameterMeta> = { … }
   ```

2. 后端若已有操作‑参数描述（你在 `/operations` 接口里已拿到）  
   - `operationsService.ts` 缓存到 SWR/react‑query；  
   - 自动合并到 `parameterConfig`，杜绝前后端字段漂移。

3. `ParameterGrid.tsx` 负责**纯渲染**：
   ```tsx
   export const ParameterGrid = ({ task }: { task: BatchTask }) => {
     return getParametersForTask(task).map(meta =>
       <ParamControl key={meta.name} meta={meta} value={task.parameters[meta.name]}
                     onChange={v => updateParam(task.id, meta.name, v)} />
     )
   }
   ```

---

## 2. 分离 **业务逻辑 / 副作用** —— `BatchProcessor`

### 2.1 自定义 Hook：`useBatchProcessor.ts`

| 角色 | 现在的位置 | 新职责 |
| ---- | ---------- | ------ |
| 任务队列状态 | `useState` 多个 scattered | `Zustand` 或 `useReducer`，暴露 `addTask / updateTask / removeTask / addFileGroup` 等 |
| 文件上传 | `startBatchProcess` 内部  | `fileService.upload(files, fileType)` |
| 批处理提交 | 同上 | `batchService.submit(taskPayload)` |
| 日志 | 直接 `onLog` | 提供 `useLog()` context |

> 这样 UI 层仅 `const { tasks, isProcessing, start } = useBatchProcessor()`，易于测试。

### 2.2 组件拆分

| 新组件 | 来源 | 说明 |
| ------ | ---- | ---- |
| **TaskCard** | `batchTasks.map(...)` 巨型 JSX  | 负责每个任务的输入/操作/参数 |
| **FileSection** | `<div className="file-section">` | 单独处理文件/文件组增删 |
| **ParameterGrid** | 原 `<div className="parameters-grid">` | 使用新 meta 渲染 |
| **BatchProcessorPage** | 顶层 | 仅布局 + 按钮 + 调用 hook |

> 组件体积目标：**< 200 行/文件**。

### 2.3 并发与错误处理

- **上传与提交并行**：  
  ```ts
  await Promise.all(validTasks.map(task => processTask(task, semaphore)))
  ```  
  使用 `p-limit` 控制并发 3‑5。
- **错误分离**：失败的任务单独 toast + 日志，但不 block 其他任务。

---

## 3. 重构 **结果与进度** —— `BatchResults`

### 3.1 `useBatchResults.ts`

| 功能 | 现状 | 重构后 |
| ---- | ---- | ------ |
| 轮询 | `setTimeout` 链式重复  | `react-query` 👉 `useQuery(['results', ids], fetchFn, { refetchInterval: 3000, enabled: ids.length>0 })` |
| 状态枚举 | `getStatusIcon/getStatusClass` 双函数  | `statusMap.ts`：`const STATUS = { SUCCESS:'success', … }` |
| 下载 | DOM 手写 `<a>`  | `utils/download.ts`，同时提供 `bulkDownload(filenames[])` |

### 3.2 组件拆分

| 新组件 | 说明 |
| ------ | ---- |
| **ResultsModal** | 包含 `<ProgressSummary>` + `<TaskResultList>` |
| **ProgressSummary** | 总进度条 + 成功/失败计数 |
| **TaskProgressCard** | 已有，可保留 |
| **ResultCard** | 统一展示缩略图+按钮；复用 `FilePreview` |

> 所有组件无副作用，只吃 props！

---

## 4. 状态管理方案

```ts
interface BatchTask {  
  id: string  
  name: string  
  fileType: 'image'|'video'  
  operation: string  
  parameters: Record<string, any>  
  files: (File | FileGroup)[]  
}
interface FileGroup { id: string; files: File[] }

export const useBatchStore = create<{
  tasks: BatchTask[]
  addTask(): void
  updateTask(id: string, data: Partial<BatchTask>): void
  …
}>()
```

- UI 与业务通过 selector 读取，避免 **props drilling**。
- Electron / Web 环境差异：在 Store 的 middleware 里自动把 `localStorage` key 前缀替换，代替文件里 scattered 判断 。

---

## 5. 渐进式迁移计划（Sprint 视图）

| Sprint | 目标 | 输出物 |
| ------ | ---- | ------ |
| 0 | 确立基线，CI、TS、目录 | ESLint、Prettier、tsconfig |
| 1 | 参数配置抽离 | `parameterConfig.ts` + `ParameterGrid.tsx` |
| 2 | 抽 Hook + 拆 UI（BatchProcessor） | `useBatchProcessor`、`TaskCard` |
| 3 | `BatchResults` 重写 + react‑query | `useBatchResults`、`ResultsModal` |
| 4 | Zustand store & 日志上下文 | `batchStore.ts`、`LogProvider` |
| 5 | 并发优化 + Service 层 | `fileService`、`batchService` |
| 6 | 单元 / 集成测试 | RTL + msw |
| 7 | 性能 & 体验 | react‑window 虚拟化、Skeleton、i18n |

---

## 6. 关键代码示例

### 6.1 `batchService.ts`
```ts
export async function processTask(task: BatchTask) {
  const files = extractFilesFromTask(task)
  const { fileIds } = await uploadFiles(files, task.fileType)
  return api.post('/process', { ...task, files: fileIds })
}
```

### 6.2 `useBatchResults.ts`
```ts
export function useBatchResults(ids: string[]) {
  return useQuery({
    queryKey: ['results', ids],
    queryFn: () => api.post('/process/results', { task_ids: ids }),
    refetchInterval: (data) =>
      data?.every(r => ['SUCCESS','FAILURE','ERROR'].includes(r.status)) ? false : 3000,
    enabled: ids.length > 0,
  })
}
```

---

## 7. 可测试性提升

- **纯函数 & util**：`fileUtils.extractFilesFromTask`、`statusMap.getClass(status)` → 100 % 单测覆盖。
- **Hook 测试**：`@testing-library/react-hooks` + `msw` mock API。
- **E2E**：Cypress 跨 Electron / Web 两种环境，确保事件链路一致。

---

## 8. 补充建议

1. **后端 SSE / WebSocket**：若批量处理耗时较长，可在第二阶段将轮询替换为实时推送。
2. **错误可观测性**：所有 service 捕获后统一 `toast + sentry`，避免控制台孤立日志。
3. **配置驱动**：未来若再新增操作，只需后端返回 meta ⇒ 前端自动生成 UI，无需改代码。

---

以上方案从 **代码层、组件层、架构层** 三个维度给出了细颗粒度的重构路径，并附带所需的新文件、接口、Hook 设计与冲刺排期，确保团队可以 **边迭代边交付**。如需任何阶段的示例实现或测试脚本，可再告诉我！