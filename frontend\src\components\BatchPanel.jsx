import React, { useState } from 'react'
import { Plus, X, Play, Folder, FileText } from 'lucide-react'
import './BatchPanel.css'

const BatchPanel = ({ currentType, onBatchProcess }) => {
  const [batchTasks, setBatchTasks] = useState([])
  const [isProcessing, setIsProcessing] = useState(false)

  // 添加批处理任务
  const addBatchTask = () => {
    const newTask = {
      id: Date.now(),
      name: `批处理任务 ${batchTasks.length + 1}`,
      files: [],
      operation: '',
      parameters: {}
    }
    setBatchTasks([...batchTasks, newTask])
  }

  // 删除批处理任务
  const removeBatchTask = (taskId) => {
    setBatchTasks(batchTasks.filter(task => task.id !== taskId))
  }

  // 更新任务
  const updateTask = (taskId, updates) => {
    setBatchTasks(batchTasks.map(task => 
      task.id === taskId ? { ...task, ...updates } : task
    ))
  }

  // 添加文件到任务
  const addFilesToTask = (taskId, files) => {
    const task = batchTasks.find(t => t.id === taskId)
    if (task) {
      const newFiles = [...task.files, ...Array.from(files)]
      updateTask(taskId, { files: newFiles })
    }
  }

  // 从任务中移除文件
  const removeFileFromTask = (taskId, fileIndex) => {
    const task = batchTasks.find(t => t.id === taskId)
    if (task) {
      const newFiles = task.files.filter((_, index) => index !== fileIndex)
      updateTask(taskId, { files: newFiles })
    }
  }

  // 开始批处理
  const startBatchProcess = async () => {
    if (batchTasks.length === 0) return
    
    setIsProcessing(true)
    try {
      if (onBatchProcess) {
        await onBatchProcess(batchTasks)
      }
    } catch (error) {
      console.error('批处理失败:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="batch-panel">
      <div className="card">
        <div className="batch-header">
          <h2 className="section-title">批处理任务</h2>
          <button 
            className="btn btn-primary"
            onClick={addBatchTask}
            disabled={isProcessing}
          >
            <Plus className="btn-icon" />
            添加任务
          </button>
        </div>

        {batchTasks.length === 0 ? (
          <div className="empty-state">
            <Folder className="empty-icon" />
            <p>暂无批处理任务</p>
            <p className="empty-description">点击"添加任务"开始创建批处理任务</p>
          </div>
        ) : (
          <div className="batch-tasks">
            {batchTasks.map(task => (
              <div key={task.id} className="batch-task">
                <div className="task-header">
                  <input
                    type="text"
                    className="input task-name"
                    value={task.name}
                    onChange={(e) => updateTask(task.id, { name: e.target.value })}
                    placeholder="任务名称"
                  />
                  <button
                    className="btn-icon-only remove-task"
                    onClick={() => removeBatchTask(task.id)}
                    disabled={isProcessing}
                  >
                    <X className="btn-icon" />
                  </button>
                </div>

                <div className="task-content">
                  {/* 文件选择 */}
                  <div className="file-section">
                    <label className="section-label">文件列表:</label>
                    <input
                      type="file"
                      multiple
                      accept={currentType === 'image' ? 'image/*' : 'video/*'}
                      onChange={(e) => addFilesToTask(task.id, e.target.files)}
                      className="file-input-hidden"
                      id={`file-input-${task.id}`}
                    />
                    <label 
                      htmlFor={`file-input-${task.id}`}
                      className="file-select-btn"
                    >
                      <Plus className="btn-icon" />
                      选择文件
                    </label>
                    
                    {task.files.length > 0 && (
                      <div className="file-list">
                        {task.files.map((file, index) => (
                          <div key={index} className="file-item">
                            <FileText className="file-icon" />
                            <span className="file-name">{file.name}</span>
                            <button
                              className="btn-icon-only remove-file"
                              onClick={() => removeFileFromTask(task.id, index)}
                            >
                              <X className="btn-icon" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* 操作选择 */}
                  <div className="operation-section">
                    <label className="section-label">操作:</label>
                    <select
                      className="select"
                      value={task.operation}
                      onChange={(e) => updateTask(task.id, { operation: e.target.value })}
                    >
                      <option value="">选择操作</option>
                      <option value="sharpen">图像锐化</option>
                      <option value="grayscale">灰度化</option>
                      <option value="gamma_correction">伽马变换</option>
                    </select>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {batchTasks.length > 0 && (
          <div className="batch-actions">
            <button
              className="btn btn-success"
              onClick={startBatchProcess}
              disabled={isProcessing || batchTasks.every(task => task.files.length === 0 || !task.operation)}
            >
              <Play className="btn-icon" />
              {isProcessing ? '处理中...' : '开始批处理'}
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default BatchPanel
