"""
图像锐化处理任务
"""
import cv2
import numpy as np
import os
import sys
import time
import logging
from celery import current_task

logger = logging.getLogger(__name__)


def safe_float(value, default=0.0):
    """安全地将值转换为浮点数"""
    if isinstance(value, (int, float)):
        return float(value)
    if isinstance(value, str):
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    return default


def sharpen_image_task(self, file_path, parameters):
    """图像锐化处理"""
    try:
        # 更新任务状态
        self.update_state(state='PROGRESS',
                         meta={'current': 10, 'total': 100,
                               'status': 'Loading image...'})

        # 读取图像
        image = cv2.imread(file_path)
        if image is None:
            raise ValueError(f"Cannot load image from {file_path}")

        self.update_state(state='PROGRESS',
                         meta={'current': 30, 'total': 100,
                               'status': 'Applying sharpening filter...'})

        # 创建锐化核
        intensity = safe_float(parameters.get('intensity', 1.0), 1.0)
        kernel = np.array([[-1,-1,-1],
                          [-1, 9,-1],
                          [-1,-1,-1]]) * intensity

        # 应用锐化滤镜
        sharpened = cv2.filter2D(image, -1, kernel)

        self.update_state(state='PROGRESS',
                         meta={'current': 80, 'total': 100,
                               'status': 'Saving result...'})

        # 保存结果
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        # 生成唯一文件名避免缓存问题 - 使用任务ID确保唯一性
        task_id = self.request.id if hasattr(self, 'request') and self.request else 'unknown'
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        ext = os.path.splitext(os.path.basename(file_path))[1]
        output_filename = f'sharpened_{base_name}_{task_id}{ext}'
        output_path = os.path.join(output_dir, output_filename)
        cv2.imwrite(output_path, sharpened)

        result = {
            'status': 'success',
            'output_path': output_path,
            'output_filename': output_filename,
            'original_path': file_path,
            'intensity_applied': intensity
        }

        # 设置任务状态为成功
        self.update_state(state='SUCCESS', meta=result)
        return result

    except Exception as e:
        # 记录错误但不调用update_state，让Celery自然处理异常
        error_msg = str(e)
        logger.error(f"Sharpen image task failed: {error_msg}")
        # 抛出一个简单的异常，避免序列化问题
        raise Exception(f"Image sharpening failed: {error_msg}")