const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// 全局变量
let mainWindow;



// 创建主窗口
function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, 'assets', 'icon.png'), // 可选：应用图标
        show: false // 先不显示，等加载完成后再显示
    });
    
    // 加载前端页面 - 简化的路径处理
    const possiblePaths = [
        // 开发环境路径
        path.join(__dirname, '..', 'frontend', 'dist', 'index.html'),
        // 生产环境路径1
        path.join(__dirname, 'frontend-dist', 'index.html'),
        // 生产环境路径2
        path.join(process.resourcesPath, 'app', 'frontend-dist', 'index.html'),
        // 备用路径
        path.join(process.cwd(), 'frontend', 'dist', 'index.html')
    ];
    
    let frontendLoaded = false;
    
    for (const frontendPath of possiblePaths) {
        console.log('Checking frontend path:', frontendPath);
        if (fs.existsSync(frontendPath)) {
            console.log('Found frontend at:', frontendPath);
            mainWindow.loadFile(frontendPath);
            frontendLoaded = true;
            break;
        }
    }
    
    if (!frontendLoaded) {
        console.error('Frontend files not found in any location');
        console.log('Searched paths:', possiblePaths);
        
        // 显示详细的错误信息
        const errorHtml = `
        <html>
        <head><title>Frontend Not Found</title></head>
        <body style="font-family: Arial, sans-serif; padding: 20px;">
            <h1>Frontend files not found</h1>
            <p>Please build the frontend first using:</p>
            <pre style="background: #f0f0f0; padding: 10px;">cd frontend && npm run build</pre>
            <h3>Searched paths:</h3>
            <ul>
                ${possiblePaths.map(p => `<li><code>${p}</code></li>`).join('')}
            </ul>
            <p>Current working directory: <code>${process.cwd()}</code></p>
            <p>__dirname: <code>${__dirname}</code></p>
        </body>
        </html>
        `;
        mainWindow.loadURL('data:text/html,' + encodeURIComponent(errorHtml));
    }
    
    // 窗口准备好后显示
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });
    
    // 开发模式下打开开发者工具
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }
    
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

// 应用事件处理
app.whenReady().then(() => {
    console.log('Electron app ready');

    // 创建窗口
    createWindow();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// IPC 通信处理
ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
