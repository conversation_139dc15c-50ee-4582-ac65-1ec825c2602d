#!/usr/bin/env python3
"""
验证修复效果的测试脚本
测试多文件处理不再返回重复结果
"""

import requests
import json
import time
import os
from pathlib import Path

API_BASE = "http://localhost:5000/api"

def test_multiple_file_processing():
    """测试多文件处理是否返回不同的结果"""
    print("🧪 开始测试多文件处理修复效果...")
    
    # 创建两个测试图片文件（简单的测试数据）
    test_files = []
    
    # 检查是否有现有的测试图片
    test_dir = Path("test_images")
    if test_dir.exists():
        image_files = list(test_dir.glob("*.jpg")) + list(test_dir.glob("*.png"))
        if len(image_files) >= 2:
            test_files = image_files[:2]
            print(f"✅ 使用现有测试图片: {[f.name for f in test_files]}")
    
    if len(test_files) < 2:
        print("❌ 需要至少2个测试图片文件在 test_images/ 目录中")
        print("请手动上传两个图片文件进行测试")
        return False
    
    try:
        # 1. 上传文件
        print("📤 上传测试文件...")
        files = []
        for i, file_path in enumerate(test_files):
            with open(file_path, 'rb') as f:
                files.append(('files', (f'test_image_{i+1}{file_path.suffix}', f.read(), 'image/jpeg')))
        
        upload_response = requests.post(f"{API_BASE}/upload", files=files)
        if upload_response.status_code != 200:
            print(f"❌ 上传失败: {upload_response.status_code}")
            return False
        
        upload_data = upload_response.json()
        print(f"✅ 上传成功: {len(upload_data['files'])} 个文件")
        
        # 2. 提交处理任务
        print("🔄 提交锐化处理任务...")
        process_data = {
            "operation": "sharpen",
            "files": upload_data['files'],
            "parameters": {"intensity": 1.5}
        }
        
        process_response = requests.post(f"{API_BASE}/process", json=process_data)
        if process_response.status_code != 200:
            print(f"❌ 处理提交失败: {process_response.status_code}")
            return False
        
        process_result = process_response.json()
        task_ids = [task['task_id'] for task in process_result['task_ids']]
        print(f"✅ 任务提交成功: {len(task_ids)} 个任务")
        print(f"📋 任务IDs: {task_ids}")
        
        # 3. 等待任务完成并检查结果
        print("⏳ 等待任务完成...")
        max_wait = 30  # 最多等待30秒
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            results_response = requests.post(f"{API_BASE}/process/results", 
                                           json={"task_ids": task_ids})
            
            if results_response.status_code != 200:
                print(f"❌ 查询结果失败: {results_response.status_code}")
                return False
            
            results_data = results_response.json()
            results = results_data.get('results', [])
            
            # 检查是否所有任务都完成
            completed_tasks = [r for r in results if r['status'] == 'SUCCESS']
            if len(completed_tasks) == len(task_ids):
                print(f"✅ 所有任务完成: {len(completed_tasks)}/{len(task_ids)}")
                
                # 4. 验证结果不重复
                output_filenames = []
                for result in completed_tasks:
                    filename = result['result']['output_filename']
                    output_filenames.append(filename)
                    print(f"📄 任务 {result['task_id'][:8]}... → {filename}")
                
                # 检查文件名是否唯一
                unique_filenames = set(output_filenames)
                if len(unique_filenames) == len(output_filenames):
                    print("🎉 测试通过！所有任务返回了不同的结果文件")
                    print(f"📊 结果统计: {len(output_filenames)} 个任务，{len(unique_filenames)} 个唯一文件")
                    return True
                else:
                    print("❌ 测试失败！发现重复的结果文件")
                    print(f"📊 结果统计: {len(output_filenames)} 个任务，{len(unique_filenames)} 个唯一文件")
                    print(f"🔍 重复文件: {[f for f in output_filenames if output_filenames.count(f) > 1]}")
                    return False
            
            print(f"⏳ 等待中... ({len(completed_tasks)}/{len(task_ids)} 完成)")
            time.sleep(2)
        
        print("❌ 测试超时，任务未在预期时间内完成")
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 多文件处理修复验证测试")
    print("=" * 50)
    
    # 检查API连接
    try:
        health_response = requests.get(f"{API_BASE}/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ API服务连接正常")
        else:
            print("❌ API服务连接异常")
            exit(1)
    except Exception as e:
        print(f"❌ 无法连接到API服务: {str(e)}")
        exit(1)
    
    # 运行测试
    success = test_multiple_file_processing()
    
    print("=" * 50)
    if success:
        print("🎉 修复验证测试通过！")
        exit(0)
    else:
        print("❌ 修复验证测试失败！")
        exit(1)
