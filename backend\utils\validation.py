"""
验证工具函数
"""
import os
from config import ALLOWED_EXTENSIONS


def allowed_file(filename, file_type='image'):
    """检查文件类型是否允许"""
    if '.' not in filename:
        return False
    extension = filename.rsplit('.', 1)[1].lower()
    return extension in ALLOWED_EXTENSIONS.get(file_type, set())


def validate_file_path(file_path, base_folder):
    """验证文件路径安全性，防止路径遍历攻击"""
    try:
        # 将路径转换为绝对路径
        abs_file_path = os.path.abspath(file_path)
        abs_base_folder = os.path.abspath(base_folder)
        
        # 检查文件路径是否在允许的基础文件夹内
        return abs_file_path.startswith(abs_base_folder)
    except Exception:
        return False


class ParameterValidator:
    """参数验证器类"""
    
    # 操作参数配置
    OPERATION_PARAMS = {
        # 基础图像处理
        'sharpen': {'intensity': (float, 0.1, 5.0)},
        'gamma_correction': {'gamma': (float, 0.1, 3.0)},
        'grayscale': {},
        'canny_edge': {
            'low_threshold': (int, 0, 255),
            'high_threshold': (int, 0, 255)
        },
        'image_fusion': {
            'weight1': (float, 0.0, 1.0),
            'weight2': (float, 0.0, 1.0)
        },
        # 高级图像处理
        'beauty_enhancement': {
            'slimming_strength': (float, 0.0, 1.0),
            'smoothing_strength': (float, 0.0, 1.0)
        },
        'image_stitching': {'mode': (str, None, None)},
        'texture_transfer': {
            'block_size': (int, 5, 50),
            'alpha_start': (float, 0.0, 1.0),
            'alpha_end': (float, 0.0, 1.0)
        },
        # 视频处理操作
        'resize': {
            'width': (int, 1, 4096),
            'height': (int, 1, 4096),
            'scale_mode': (str, None, None),
            'scale_ratio': (float, 0.1, 3.0)
        },
        'binary': {
            'threshold': (int, 0, 255),
            'max_value': (int, 0, 255),
            'threshold_type': (str, None, None)
        },
        'blur': {
            'filter_type': (str, None, None),
            'kernel_size': (int, 1, 51),
            'sigma': (float, 0.1, 10.0)
        },
        'edge_detection': {
            'low_threshold': (int, 0, 255),
            'high_threshold': (int, 0, 255),
            'edge_type': (str, None, None)
        },
        'transform': {
            'transform_type': (str, None, None),
            'angle': (float, -360.0, 360.0)
        },
        'extract_frame': {
            'frame_number': (int, 0, 999999)
        }
    }
    
    # 字符串参数的有效值
    STRING_PARAM_VALUES = {
        ('image_stitching', 'mode'): ['panorama', 'scans'],
        ('binary', 'threshold_type'): ['binary', 'binary_inv', 'trunc', 'tozero', 'tozero_inv'],
        ('blur', 'filter_type'): ['gaussian', 'median', 'bilateral', 'sharpen'],
        ('edge_detection', 'edge_type'): ['canny', 'sobel', 'laplacian'],
        ('transform', 'transform_type'): ['rotate_90', 'rotate_180', 'rotate_270', 'flip_horizontal', 'flip_vertical', 'flip_both', 'rotate_custom'],
        ('resize', 'scale_mode'): ['fit', 'fill', 'stretch', 'crop', 'ratio', 'custom']
    }
    
    @classmethod
    def validate_parameters(cls, operation, parameters):
        """验证操作参数"""
        if operation not in cls.OPERATION_PARAMS:
            return False, f"Unknown operation: {operation}"
        
        operation_params = cls.OPERATION_PARAMS[operation]
        
        for param_name, (param_type, min_val, max_val) in operation_params.items():
            if param_name in parameters:
                try:
                    if param_type == str:
                        # 字符串参数验证
                        valid_values = cls.STRING_PARAM_VALUES.get((operation, param_name))
                        if valid_values and parameters[param_name] not in valid_values:
                            return False, f"Parameter {param_name} must be one of: {', '.join(valid_values)}"
                        parameters[param_name] = str(parameters[param_name])
                    elif param_type == bool:
                        parameters[param_name] = bool(parameters[param_name])
                    else:
                        value = param_type(parameters[param_name])
                        if min_val is not None and max_val is not None:
                            if not (min_val <= value <= max_val):
                                return False, f"Parameter {param_name} must be between {min_val} and {max_val}"
                        parameters[param_name] = value
                except (ValueError, TypeError):
                    return False, f"Parameter {param_name} must be of type {param_type.__name__}"
        
        return True, "Valid parameters"


def validate_parameters(operation, parameters):
    """向后兼容的参数验证函数"""
    return ParameterValidator.validate_parameters(operation, parameters)
