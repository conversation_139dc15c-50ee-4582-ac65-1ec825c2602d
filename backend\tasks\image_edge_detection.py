"""
图像边缘检测任务
"""
import cv2
import os
import sys
import time
import logging
from celery import current_task

logger = logging.getLogger(__name__)


def safe_int(value, default=0):
    """安全地将值转换为整数"""
    if isinstance(value, int):
        return value
    if isinstance(value, (float, str)):
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return default
    return default


def canny_edge_detection_task(self, file_path, parameters):
    """Canny边缘检测"""
    try:
        self.update_state(state='PROGRESS',
                         meta={'current': 10, 'total': 100,
                               'status': 'Loading image...'})

        image = cv2.imread(file_path)
        if image is None:
            raise ValueError(f"Cannot load image from {file_path}")

        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        self.update_state(state='PROGRESS',
                         meta={'current': 50, 'total': 100,
                               'status': 'Detecting edges...'})

        # 获取参数
        low_threshold = safe_int(parameters.get('low_threshold', 50), 50)
        high_threshold = safe_int(parameters.get('high_threshold', 150), 150)

        # 应用Canny边缘检测
        edges = cv2.Canny(gray, low_threshold, high_threshold)

        self.update_state(state='PROGRESS',
                         meta={'current': 80, 'total': 100,
                               'status': 'Saving result...'})

        # 保存结果
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        # 生成唯一文件名避免缓存问题 - 使用任务ID确保唯一性
        task_id = self.request.id if hasattr(self, 'request') and self.request else 'unknown'
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        ext = os.path.splitext(os.path.basename(file_path))[1]
        output_filename = f'edges_{base_name}_{task_id}{ext}'
        output_path = os.path.join(output_dir, output_filename)
        cv2.imwrite(output_path, edges)

        result = {
            'status': 'success',
            'output_path': output_path,
            'output_filename': output_filename,
            'original_path': file_path,
            'low_threshold': low_threshold,
            'high_threshold': high_threshold
        }

        # 设置任务状态为成功
        self.update_state(state='SUCCESS', meta=result)
        return result

    except Exception as e:
        error_msg = str(e)
        logger.error(f"Canny edge detection task failed: {error_msg}")
        raise Exception(f"Edge detection failed: {error_msg}")