.batch-processor {
  height: 100%;
}

.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.mode-switch {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 4px;
  gap: 2px;
}

.mode-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mode-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.mode-btn.active {
  background: rgba(99, 102, 241, 0.8);
  color: white;
}

.mode-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.mode-icon {
  width: 14px;
  height: 14px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.7);
}

.empty-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 15px;
  opacity: 0.5;
}

.empty-description {
  font-size: 14px;
  margin-top: 5px;
}

.batch-tasks {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
}

.batch-task {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.batch-task:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.task-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.task-name {
  flex: 1;
  font-weight: 500;
  font-size: 16px;
}

.btn-icon-only {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-task {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.remove-task:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.task-row {
  display: grid;
  grid-template-columns: 100px 1fr;
  gap: 12px;
  align-items: center;
}

.task-label {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.parameters-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.parameters-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.param-icon {
  width: 16px;
  height: 16px;
}

.parameters-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.no-parameters {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px dashed rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.no-parameters-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  font-style: italic;
}

.parameter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.parameter-label {
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  text-transform: capitalize;
}

.parameter-input {
  font-size: 14px;
}

/* 滑块样式 */
.parameter-slider-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.slider-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.parameter-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
  outline: none;
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-appearance: none;
  appearance: none;
}

.parameter-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.parameter-slider::-webkit-slider-thumb:hover {
  background: #6366f1;
  transform: scale(1.1);
}

.parameter-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.parameter-slider::-moz-range-thumb:hover {
  background: #6366f1;
  transform: scale(1.1);
}

.parameter-slider:focus {
  background: rgba(255, 255, 255, 0.15);
}

.slider-value {
  min-width: 50px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #4f46e5;
  background: rgba(79, 70, 229, 0.1);
  border: 1px solid rgba(79, 70, 229, 0.3);
  border-radius: 6px;
  padding: 4px 8px;
}

.slider-range {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: -4px;
}

.parameter-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  line-height: 1.3;
}

.file-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.file-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.no-operation-hint {
  padding: 6px 12px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 4px;
  border-left: 3px solid #ffc107;
}

.hint-text {
  color: #ffc107;
  font-size: 12px;
  font-weight: 500;
}

.file-section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.file-input-hidden {
  display: none;
}

.file-select-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  width: fit-content;
}

.file-select-btn:hover:not(.disabled) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.file-select-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.file-input-label {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 600;
  width: fit-content;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.file-input-label:hover {
  background: linear-gradient(135deg, #5855eb, #7c3aed);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.file-input-label:disabled,
.file-input-label.disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.single-file-selected {
  padding: 12px 16px;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 8px;
  color: #22c55e;
  font-size: 14px;
  font-weight: 500;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
  width: 100%;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.file-item:hover {
  background: rgba(255, 255, 255, 0.08);
}

.file-icon {
  width: 16px;
  height: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.file-name {
  flex: 1;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  word-break: break-all;
}

.remove-file {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  padding: 4px;
}

.remove-file:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

.batch-actions {
  margin-top: 25px;
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 滚动条样式 */
.batch-tasks::-webkit-scrollbar {
  width: 6px;
}

.batch-tasks::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.batch-tasks::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.batch-tasks::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .task-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .task-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .parameters-grid {
    grid-template-columns: 1fr;
  }
  
  .batch-tasks {
    max-height: 300px;
  }
}

@media (max-width: 480px) {
  .batch-task {
    padding: 16px;
  }
  
  .empty-state {
    padding: 40px 15px;
  }
  
  .empty-icon {
    width: 40px;
    height: 40px;
  }
  
  .file-item {
    padding: 8px 10px;
  }
  
  .file-name {
    font-size: 13px;
  }
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 新增的视频处理参数组件样式 */
.parameter-select-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.parameter-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.parameter-select option {
  background: #2a2a2a;
  color: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
}

.parameter-select:focus {
  outline: none;
  border-color: #4ade80;
  box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.2);
}

.parameter-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.parameter-checkbox-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.parameter-checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.parameter-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #4ade80;
  cursor: pointer;
}

.parameter-checkbox:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checkbox-text {
  font-weight: 500;
  user-select: none;
}

/* 输入框组件样式 */
.parameter-input-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.parameter-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  transition: all 0.2s ease;
}

.parameter-input:focus {
  outline: none;
  border-color: #4ade80;
  box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.2);
}

.parameter-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 视频帧选择器样式 */
.video-frame-picker {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.frame-picker-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.frame-picker-info span {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.frame-picker-info .btn {
  padding: 6px 12px;
  font-size: 12px;
}

/* 摄像头采集专用界面样式 */
.camera-capture-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 16px;
}

.camera-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.camera-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.camera-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4ade80;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.camera-record-btn {
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
}

.camera-preview {
  width: 100%;
  height: 200px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-placeholder {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

.preview-placeholder span {
  display: block;
  font-size: 16px;
  margin-bottom: 8px;
}

.preview-placeholder small {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.4);
}

/* 文件限制信息样式 */
.file-limit-info {
  padding: 6px 12px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 4px;
  border-left: 3px solid #ffc107;
}

.file-limit-info small {
  color: #ffc107;
  font-size: 12px;
  font-weight: 500;
}
