"""
视频变换处理任务（旋转、翻转等）
"""
import os
import logging
from celery import current_task

from tasks.opencv_ffmpeg_processor import OpenCVFFmpegProcessor, create_opencv_processor

logger = logging.getLogger(__name__)


def create_celery_progress_callback(task_instance):
    """创建Celery进度回调函数"""
    def progress_callback(current, total, status="Processing"):
        task_instance.update_state(
            state='PROGRESS',
            meta={'current': current, 'total': total, 'status': status}
        )
    return progress_callback


def transform_video_task(self, file_path, parameters):
    """视频变换处理（旋转、翻转等）"""
    try:
        progress_callback = create_celery_progress_callback(self)
        
        # 获取变换参数
        transform_type = parameters.get('transform_type', 'rotate_90')
        angle = parameters.get('angle', 90)
        
        # 创建变换处理器
        opencv_processor = create_opencv_processor(
            'transform', 
            transform_type=transform_type, 
            angle=angle
        )
        
        with OpenCVFFmpegProcessor(file_path, preserve_audio=True) as processor:
            result = processor.process_frames_with_opencv(
                opencv_processor=opencv_processor,
                output_prefix=f'{transform_type}_transform',
                progress_callback=progress_callback
            )
            
            # 添加变换参数到结果
            result.update({
                'transform_type': transform_type,
                'angle': angle if transform_type == 'rotate_custom' else None
            })
            
            self.update_state(state='SUCCESS', meta=result)
            return result
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Transform video task failed: {error_msg}")
        self.update_state(state='FAILURE', meta={'error': error_msg})
        raise Exception(f"Video transform processing failed: {error_msg}")