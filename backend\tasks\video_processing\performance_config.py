"""
视频处理性能配置管理模块
"""

import os
import multiprocessing as mp
import psutil
import logging

logger = logging.getLogger(__name__)


class PerformanceConfig:
    """性能配置管理类"""

    def __init__(self):
        # 自动检测系统资源
        self.cpu_count = mp.cpu_count()
        self.available_memory = psutil.virtual_memory().available

        # 检测是否在Celery环境中运行
        self.is_celery_worker = self._detect_celery_environment()

        # 并行处理配置
        if self.is_celery_worker:
            # Celery环境：使用线程池，更保守的配置
            self.use_thread_pool = True
            self.max_workers = min(self.cpu_count, 6)  # 线程数可以稍多一些
            self.batch_size = 20  # 较小的批处理大小
        else:
            # 非Celery环境：使用进程池，更激进的配置
            self.use_thread_pool = False
            self.max_workers = min(self.cpu_count, 8)
            self.batch_size = 30

        self.memory_limit_mb = min(2048, self.available_memory // (1024 * 1024) // 4)  # 使用1/4可用内存

        # FFmpeg优化配置
        self.ffmpeg_threads = self.cpu_count
        self.enable_hardware_accel = True

        # 进度回调优化
        self.progress_update_interval = 10  # 每处理10帧更新一次进度

        pool_type = "ThreadPool" if self.use_thread_pool else "ProcessPool"
        env_type = "Celery" if self.is_celery_worker else "Standalone"
        logger.info(f"Performance config ({env_type}): {self.max_workers} workers ({pool_type}), "
                   f"batch_size={self.batch_size}, memory_limit={self.memory_limit_mb}MB")

    def _detect_celery_environment(self) -> bool:
        """检测是否在Celery worker环境中运行"""
        try:
            # 检查当前进程是否是daemon进程
            current_process = mp.current_process()
            if hasattr(current_process, 'daemon') and current_process.daemon:
                return True

            # 检查环境变量
            if 'CELERY_WORKER' in os.environ:
                return True

            # 检查进程名称
            if 'celery' in current_process.name.lower():
                return True

            return False
        except Exception:
            return False

    def adjust_for_video_size(self, width: int, height: int, total_frames: int):
        """根据视频尺寸调整配置"""
        # 估算单帧内存使用（RGB，3通道）
        frame_size_mb = (width * height * 3) / (1024 * 1024)

        # 调整批处理大小以控制内存使用
        max_batch_by_memory = max(1, int(self.memory_limit_mb / (frame_size_mb * self.max_workers)))
        self.batch_size = min(self.batch_size, max_batch_by_memory)

        # 对于大视频，减少并行度
        if frame_size_mb > 10:  # 大于10MB的帧
            self.max_workers = min(self.max_workers, 4)

        logger.info(f"Adjusted config for {width}x{height}: "
                   f"batch_size={self.batch_size}, workers={self.max_workers}")
