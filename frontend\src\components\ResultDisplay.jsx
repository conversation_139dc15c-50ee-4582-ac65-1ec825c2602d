import React from 'react'
import { CheckCircle, AlertCircle, Download } from 'lucide-react'
import './ResultDisplay.css'

const ResultDisplay = ({ result }) => {
  if (!result) return null

  return (
    <div className={`result-container card ${result.success ? 'success' : 'error'}`}>
      <div className="result-header">
        {result.success ? (
          <CheckCircle className="result-icon success-icon" />
        ) : (
          <AlertCircle className="result-icon error-icon" />
        )}
        <h3 className="result-title">
          {result.success ? '处理完成' : '处理失败'}
        </h3>
      </div>
      
      <div className="result-content">
        <p className="result-message">{result.message}</p>
        
        {result.success && result.data && (
          <div className="result-actions">
            <button className="btn btn-success">
              <Download className="btn-icon" />
              下载结果
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default ResultDisplay
