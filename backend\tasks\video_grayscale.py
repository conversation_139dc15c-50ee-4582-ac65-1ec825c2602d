"""
视频灰度化处理任务
"""
import os
import logging
from celery import current_task

from tasks.opencv_ffmpeg_processor import OpenCVFFmpegProcessor, create_opencv_processor

logger = logging.getLogger(__name__)


def create_celery_progress_callback(task_instance):
    """创建Celery进度回调函数"""
    def progress_callback(current, total, status="Processing"):
        task_instance.update_state(
            state='PROGRESS',
            meta={'current': current, 'total': total, 'status': status}
        )
    return progress_callback


def grayscale_video_task(self, file_path, parameters):
    """视频灰度化处理"""
    try:
        progress_callback = create_celery_progress_callback(self)
        
        # 创建灰度化处理器
        opencv_processor = create_opencv_processor('grayscale')
        
        with OpenCVFFmpegProcessor(file_path, preserve_audio=True) as processor:
            result = processor.process_frames_with_opencv(
                opencv_processor=opencv_processor,
                output_prefix='grayscale',
                progress_callback=progress_callback
            )
            
            self.update_state(state='SUCCESS', meta=result)
            return result
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Grayscale video task failed: {error_msg}")
        self.update_state(state='FAILURE', meta={'error': error_msg})
        raise Exception(f"Video grayscale processing failed: {error_msg}")