# ===================================
# Clover Project .gitignore
# ===================================

# ===================================
# Operating System Files
# ===================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# IDE and Editor Files
# ===================================

# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains IDEs
.idea/
*.swp
*.swo
*~

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*.tmp

# ===================================
# Node.js / Frontend (React + Vite)
# ===================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
frontend/dist/
frontend/build/
frontend/.vite/

# Cache
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# ===================================
# Python / Backend (Flask + Celery)
# ===================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
backend/build/
backend/develop-eggs/
backend/dist/
backend/downloads/
backend/eggs/
backend/.eggs/
backend/lib/
backend/lib64/
backend/parts/
backend/sdist/
backend/var/
backend/wheels/
backend/share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Virtual environments
backend/venv/
backend/.venv/
backend/env/
backend/.env/
backend/ENV/
backend/env.bak/
backend/venv.bak/

# Celery
celerybeat-schedule
celerybeat.pid

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# ===================================
# Electron Desktop App
# ===================================

# Electron build outputs
electron-app/dist/
electron-app/release/
electron-app/output/

# Electron cache
electron-app/.electron/

# ===================================
# Docker
# ===================================

# Docker volumes and data
docker-data/
.docker/

# ===================================
# Application Specific
# ===================================

# Upload directories
uploads/
backend/uploads/
electron-app/uploads/

# Thumbnail cache
thumbnails/
backend/thumbnails/
electron-app/thumbnails/

# Output directories
output/
backend/output/
electron-app/output/

# Log files
*.log
backend/logs/
backend/*.log
electron-app/*.log

# Test files
test_files/
frontend/test_files/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# ===================================
# Security and Sensitive Data
# ===================================

# API keys and secrets
.env
.env.*
!.env.example
config/secrets.json
secrets/
*.key
*.pem
*.p12
*.pfx

# Database files
*.db
*.sqlite
*.sqlite3

# ===================================
# Backup Files
# ===================================

# Backup files
*.backup
*.bak
*.old
*~

# ===================================
# Miscellaneous
# ===================================

# Compressed files
*.zip
*.tar.gz
*.rar
*.7z

# Large media files (optional - uncomment if needed)
# *.mp4
# *.avi
# *.mov
# *.wmv
# *.flv
# *.webm
# *.mkv
