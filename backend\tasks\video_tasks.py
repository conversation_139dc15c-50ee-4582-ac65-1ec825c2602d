"""
视频处理任务统一入口 - 重构后版本
"""
import os
import logging

logger = logging.getLogger(__name__)


def process_video_task(self, file_path, operation, parameters):
    """通用视频处理任务入口 - 委托给具体的任务函数"""
    try:
        # 根据操作类型调用相应的具体任务函数
        if operation == 'grayscale':
            from .video_grayscale import grayscale_video_task
            return grayscale_video_task(self, file_path, parameters)
        
        elif operation == 'resize':
            from .video_resize import resize_video_task
            return resize_video_task(self, file_path, parameters)
        
        elif operation == 'binary':
            from .video_binary import binary_video_task
            return binary_video_task(self, file_path, parameters)
        
        elif operation == 'blur':
            from .video_blur import blur_video_task
            return blur_video_task(self, file_path, parameters)
        
        elif operation == 'edge_detection':
            from .video_edge_detection import edge_detection_video_task
            return edge_detection_video_task(self, file_path, parameters)
        
        elif operation == 'transform':
            from .video_transform import transform_video_task
            return transform_video_task(self, file_path, parameters)
        
        elif operation == 'extract_frame':
            from .video_extract_frame import extract_video_frame_task
            return extract_video_frame_task(self, file_path, parameters)
        
        else:
            raise ValueError(f"Unknown video operation: {operation}")
        
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Video processing task failed: {error_msg}")
        self.update_state(state='FAILURE', meta={'error': error_msg})
        raise Exception(f"Video processing failed: {error_msg}")