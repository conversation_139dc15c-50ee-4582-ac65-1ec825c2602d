"""
文件预览路由
"""
import os
from flask import Blueprint, send_file, abort
from config import OUTPUT_FOLDER
from utils.validation import validate_file_path

preview_bp = Blueprint('preview', __name__)


@preview_bp.route('/api/preview/<filename>', methods=['GET'])
def preview_file(filename):
    """文件预览接口"""
    try:
        # 构造文件路径
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        file_path = os.path.abspath(file_path)
        
        # 验证文件路径安全性
        abs_output_folder = os.path.abspath(OUTPUT_FOLDER)
        if not validate_file_path(file_path, abs_output_folder):
            abort(403)  # Forbidden
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            abort(404)  # Not Found
        
        # 发送文件（用于预览，不强制下载）
        return send_file(file_path)
    
    except Exception as e:
        print(f"Preview file access error: {e}")
        abort(500)
