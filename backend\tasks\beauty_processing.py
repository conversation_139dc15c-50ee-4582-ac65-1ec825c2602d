"""
简化但有效的美颜处理模块
包含磨皮和瘦脸的整合美颜功能
"""

import cv2
import numpy as np
from celery import current_task
import os
import logging
import time

logger = logging.getLogger(__name__)

def get_face_cascade_path():
    """获取正确的人脸检测模型路径"""
    # 尝试多个可能的路径
    possible_paths = [
        # 虚拟环境路径
        '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/cv2/data/haarcascade_frontalface_default.xml',
        '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/cv2/data/haarcascade_frontalface_alt.xml',
        # 系统级路径
        '/usr/local/lib/python3.12/dist-packages/cv2/data/haarcascade_frontalface_default.xml',
        '/usr/local/lib/python3.12/dist-packages/cv2/data/haarcascade_frontalface_alt.xml',
        # 通过cv2模块动态查找
        os.path.join(os.path.dirname(cv2.__file__), 'data', 'haarcascade_frontalface_default.xml'),
        os.path.join(os.path.dirname(cv2.__file__), 'data', 'haarcascade_frontalface_alt.xml'),
        # 相对路径
        'haarcascade_frontalface_default.xml',
    ]

    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"Found face cascade at: {path}")
            return path

    # 如果都找不到，尝试下载或创建一个简单的替代方案
    logger.error("No face detection model found in any of the expected locations")
    logger.error(f"Searched paths: {possible_paths}")
    raise FileNotFoundError("No face detection model found")

def detect_face(image):
    """检测人脸区域"""
    try:
        cascade_path = get_face_cascade_path()
        face_cascade = cv2.CascadeClassifier(cascade_path)
        
        if face_cascade.empty():
            raise ValueError("Failed to load face detection model")
        
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(
            gray, 
            scaleFactor=1.1, 
            minNeighbors=5, 
            minSize=(50, 50)
        )
        
        if len(faces) == 0:
            return None
        
        # 选择最大的人脸
        largest_face = max(faces, key=lambda face: face[2] * face[3])
        return largest_face
        
    except Exception as e:
        logger.error(f"Face detection failed: {e}")
        return None

def apply_face_slimming(image, face_rect, strength):
    """应用瘦脸效果 - 增强版本，即使无精确人脸检测也有效果"""
    if strength <= 0:
        return image

    h, w = image.shape[:2]
    result = image.copy()

    # 如果有人脸检测结果，使用精确位置
    if face_rect is not None:
        x, y, face_w, face_h = face_rect
        left_cheek_x = x + int(face_w * 0.25)
        left_cheek_y = y + int(face_h * 0.6)
        right_cheek_x = x + int(face_w * 0.75)
        right_cheek_y = y + int(face_h * 0.6)
        center_x = x + face_w // 2
        center_y = y + int(face_h * 0.5)
        radius = int(face_w * 0.15)
    else:
        # 如果没有人脸检测，使用图像中心区域的估算位置
        # 假设人脸在图像中心区域
        face_w = int(w * 0.4)  # 假设人脸宽度为图像宽度的40%
        face_h = int(h * 0.5)  # 假设人脸高度为图像高度的50%
        x = (w - face_w) // 2
        y = int(h * 0.2)  # 人脸通常在图像上部

        left_cheek_x = x + int(face_w * 0.25)
        left_cheek_y = y + int(face_h * 0.6)
        right_cheek_x = x + int(face_w * 0.75)
        right_cheek_y = y + int(face_h * 0.6)
        center_x = w // 2
        center_y = y + int(face_h * 0.5)
        radius = int(face_w * 0.15)

        logger.info(f"No face detected, using estimated positions: center=({center_x},{center_y}), radius={radius}")
    
    def slim_cheek(cheek_x, cheek_y, center_x, center_y):
        """瘦脸处理"""
        # 创建变形映射
        map_x = np.zeros((h, w), dtype=np.float32)
        map_y = np.zeros((h, w), dtype=np.float32)
        
        # 初始化映射为原始坐标
        for i in range(h):
            for j in range(w):
                map_x[i, j] = j
                map_y[i, j] = i
        
        # 在脸颊区域应用收缩效果
        for i in range(max(0, cheek_y - radius), min(h, cheek_y + radius)):
            for j in range(max(0, cheek_x - radius), min(w, cheek_x + radius)):
                # 计算距离脸颊中心的距离
                dist = np.sqrt((j - cheek_x)**2 + (i - cheek_y)**2)
                
                if dist < radius:
                    # 计算收缩因子
                    factor = (1 - dist / radius) * strength
                    
                    # 计算向中心收缩的方向
                    dx = center_x - j
                    dy = center_y - i
                    
                    # 应用收缩
                    map_x[i, j] = j + dx * factor * 0.3
                    map_y[i, j] = i + dy * factor * 0.3
        
        return map_x, map_y
    
    # 处理左脸颊
    map_x, map_y = slim_cheek(left_cheek_x, left_cheek_y, center_x, center_y)
    result = cv2.remap(result, map_x, map_y, cv2.INTER_LINEAR)
    
    # 处理右脸颊
    map_x, map_y = slim_cheek(right_cheek_x, right_cheek_y, center_x, center_y)
    result = cv2.remap(result, map_x, map_y, cv2.INTER_LINEAR)
    
    logger.info(f"Face slimming applied with strength: {strength}")
    return result

def apply_skin_smoothing(image, face_rect, strength):
    """应用磨皮效果 - 强化版本，即使无人脸检测也有明显效果"""
    if strength <= 0:
        return image

    result = image.copy()

    # 如果有人脸区域，重点处理人脸
    if face_rect is not None:
        x, y, w, h = face_rect
        # 扩展处理区域
        x = max(0, x - int(w * 0.1))
        y = max(0, y - int(h * 0.1))
        w = min(image.shape[1] - x, int(w * 1.2))
        h = min(image.shape[0] - y, int(h * 1.2))

        face_region = result[y:y+h, x:x+w]
    else:
        # 如果没有检测到人脸，处理整个图像，但使用更强的效果
        face_region = result
        x, y = 0, 0

    # 强化的双边滤波参数 - 确保有明显效果
    d = int(20 + strength * 30)  # 增大滤波直径
    sigma_color = 100 + strength * 150  # 增强颜色相似性
    sigma_space = 100 + strength * 150  # 增强空间相似性

    # 多层磨皮处理
    smoothed = face_region.copy()

    # 第一层：双边滤波
    smoothed = cv2.bilateralFilter(smoothed, d, sigma_color, sigma_space)

    # 第二层：高斯模糊（轻微）
    if strength > 0.5:
        kernel_size = int(5 + strength * 10)
        if kernel_size % 2 == 0:
            kernel_size += 1
        gaussian_blur = cv2.GaussianBlur(smoothed, (kernel_size, kernel_size), 0)
        smoothed = cv2.addWeighted(smoothed, 0.7, gaussian_blur, 0.3, 0)

    # 第三层：边缘保护
    # 检测边缘并保护它们
    gray = cv2.cvtColor(face_region, cv2.COLOR_BGR2GRAY)
    edges = cv2.Canny(gray, 50, 150)
    edges = cv2.dilate(edges, np.ones((3,3), np.uint8), iterations=1)
    edges_3ch = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR) / 255.0

    # 在边缘区域保留更多原图细节
    edge_protected = smoothed * (1 - edges_3ch) + face_region * edges_3ch
    edge_protected = edge_protected.astype(np.uint8)

    # 强化混合 - 确保效果明显
    alpha = 0.5 + strength * 0.8  # 增强混合强度
    blended = cv2.addWeighted(face_region, 1 - alpha, edge_protected, alpha, 0)

    # 将处理后的区域放回原图
    if face_rect is not None:
        result[y:y+h, x:x+w] = blended
    else:
        result = blended

    logger.info(f"Enhanced skin smoothing applied with strength: {strength}")
    return result

def beauty_enhancement_task(self, file_path, parameters):
    """综合美颜处理（磨皮 + 瘦脸）"""
    try:
        self.update_state(state='PROGRESS',
                         meta={'current': 10, 'total': 100,
                               'status': 'Loading image...'})
        
        # 读取图像
        image = cv2.imread(file_path)
        if image is None:
            raise ValueError(f"Cannot load image from {file_path}")
        
        self.update_state(state='PROGRESS',
                         meta={'current': 30, 'total': 100,
                               'status': 'Detecting faces...'})
        
        # 检测人脸
        face_rect = detect_face(image)
        
        # 获取参数
        slimming_strength = max(0.0, min(1.0, parameters.get('slimming_strength', 0.5)))
        smoothing_strength = max(0.0, min(1.0, parameters.get('smoothing_strength', 0.5)))
        
        logger.info(f"Beauty enhancement - slimming: {slimming_strength}, smoothing: {smoothing_strength}")
        logger.info(f"Face detected: {face_rect is not None}")
        
        result = image.copy()
        
        self.update_state(state='PROGRESS',
                         meta={'current': 50, 'total': 100,
                               'status': 'Applying face slimming...'})
        
        # 应用瘦脸效果
        if slimming_strength > 0:
            result = apply_face_slimming(result, face_rect, slimming_strength)
        
        self.update_state(state='PROGRESS',
                         meta={'current': 70, 'total': 100,
                               'status': 'Applying skin smoothing...'})
        
        # 应用磨皮效果
        if smoothing_strength > 0:
            result = apply_skin_smoothing(result, face_rect, smoothing_strength)
        
        self.update_state(state='PROGRESS',
                         meta={'current': 90, 'total': 100,
                               'status': 'Saving result...'})
        
        # 保存结果
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = int(time.time() * 1000)
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        ext = os.path.splitext(os.path.basename(file_path))[1]
        output_filename = f'beauty_{base_name}_{timestamp}{ext}'
        output_path = os.path.join(output_dir, output_filename)
        cv2.imwrite(output_path, result)

        self.update_state(state='PROGRESS',
                         meta={'current': 100, 'total': 100,
                               'status': 'Completed'})

        logger.info(f"Beauty enhancement completed successfully")

        return {
            'status': 'success',
            'output_path': output_path,
            'output_filename': output_filename,
            'original_path': file_path,
            'face_detected': face_rect is not None,
            'slimming_strength_applied': slimming_strength,
            'smoothing_strength_applied': smoothing_strength
        }
        
    except Exception as e:
        logger.error(f"Beauty enhancement error: {str(e)}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise
