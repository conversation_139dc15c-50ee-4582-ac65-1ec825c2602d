import React, { useState, useEffect } from 'react'
import { Play, RotateCcw, Loader2 } from 'lucide-react'
import { useApi } from '../contexts/ApiContext'
import './OperationPanel.css'

const OperationPanel = ({
  currentType,
  selectedOperation,
  onOperationSelect,
  parameters,
  onParametersChange,
  onProcess,
  onClear,
  isProcessing,
  hasFile
}) => {
  const { getOperations } = useApi()
  const [operations, setOperations] = useState({})
  const [loading, setLoading] = useState(false)

  // 获取操作列表
  useEffect(() => {
    const fetchOperations = async () => {
      setLoading(true)
      try {
        const response = await fetch(`${window.API_BASE || 'http://localhost:5000/api'}/operations`)
        if (response.ok) {
          const data = await response.json()
          setOperations(data.operations || {})
        }
      } catch (error) {
        console.error('获取操作列表失败:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchOperations()
  }, [])

  const currentOperations = operations[currentType] || []
  const selectedOp = currentOperations.find(op => op.id === selectedOperation)

  const handleParameterChange = (paramName, value) => {
    onParametersChange({
      ...parameters,
      [paramName]: value
    })
  }

  const renderParameterInput = (paramName) => {
    const value = parameters[paramName] || ''

    // 调试信息
    console.log('Rendering parameter:', paramName, 'with value:', value)

    // 获取参数的默认值和范围
    const getParameterConfig = (param) => {
      const configs = {
        // 视频尺寸参数
        'width': { type: 'range', min: 320, max: 1920, default: 640, step: 1, unit: 'px' },
        'height': { type: 'range', min: 240, max: 1080, default: 480, step: 1, unit: 'px' },

        // 阈值参数
        'threshold': { type: 'range', min: 0, max: 255, default: 127, step: 1, unit: '' },
        'max_value': { type: 'range', min: 0, max: 255, default: 255, step: 1, unit: '' },
        'low_threshold': { type: 'range', min: 0, max: 255, default: 50, step: 1, unit: '' },
        'high_threshold': { type: 'range', min: 0, max: 255, default: 150, step: 1, unit: '' },

        // 滤波参数
        'kernel_size': { type: 'range', min: 1, max: 51, default: 5, step: 2, unit: 'px' },
        'sigma': { type: 'range', min: 0.1, max: 10.0, default: 1.0, step: 0.1, unit: '' },

        // 角度参数
        'angle': { type: 'range', min: -360, max: 360, default: 90, step: 1, unit: '°' },

        // 帧率和时长
        'fps': { type: 'range', min: 1, max: 60, default: 30, step: 1, unit: 'fps' },
        'duration': { type: 'range', min: 1, max: 300, default: 10, step: 1, unit: 's' },
        'frame_number': { type: 'range', min: 0, max: 9999, default: 0, step: 1, unit: '' },

        // 下拉选择参数
        'threshold_type': {
          type: 'select',
          options: [
            { value: 'binary', label: '二值化' },
            { value: 'binary_inv', label: '反二值化' },
            { value: 'trunc', label: '截断' },
            { value: 'tozero', label: '阈值化为零' },
            { value: 'tozero_inv', label: '反阈值化为零' }
          ],
          default: 'binary'
        },
        'filter_type': {
          type: 'select',
          options: [
            { value: 'gaussian', label: '高斯滤波' },
            { value: 'median', label: '中值滤波' },
            { value: 'bilateral', label: '双边滤波' },
            { value: 'sharpen', label: '锐化滤波' }
          ],
          default: 'gaussian'
        },
        'edge_type': {
          type: 'select',
          options: [
            { value: 'canny', label: 'Canny边缘检测' },
            { value: 'sobel', label: 'Sobel边缘检测' },
            { value: 'laplacian', label: '拉普拉斯边缘检测' }
          ],
          default: 'canny'
        },
        'transform_type': {
          type: 'select',
          options: [
            { value: 'rotate_90', label: '顺时针旋转90°' },
            { value: 'rotate_180', label: '旋转180°' },
            { value: 'rotate_270', label: '逆时针旋转90°' },
            { value: 'flip_horizontal', label: '水平翻转' },
            { value: 'flip_vertical', label: '垂直翻转' },
            { value: 'flip_both', label: '水平垂直翻转' },
            { value: 'rotate_custom', label: '自定义角度旋转' }
          ],
          default: 'rotate_90'
        },
        'processing_type': {
          type: 'select',
          options: [
            { value: 'none', label: '无处理' },
            { value: 'grayscale', label: '灰度化' },
            { value: 'blur', label: '模糊' },
            { value: 'edge', label: '边缘检测' },
            { value: 'face_detection', label: '人脸检测' }
          ],
          default: 'none'
        },
        'scale_mode': {
          type: 'select',
          options: [
            { value: 'fit', label: '适应缩放' },
            { value: 'fill', label: '填充缩放' },
            { value: 'stretch', label: '拉伸缩放' },
            { value: 'crop', label: '裁剪缩放' }
          ],
          default: 'fit'
        },
        'scale_ratio': { type: 'range', min: 0.1, max: 3.0, default: 1.0, step: 0.1, unit: 'x' },
        // 智能美颜参数
        'slimming_strength': { type: 'range', min: 0.0, max: 1.0, default: 0.3, step: 0.1, unit: '' },
        'smoothing_strength': { type: 'range', min: 0.0, max: 1.0, default: 0.5, step: 0.1, unit: '' },
        // 图像拼接模式
        'mode': {
          type: 'select',
          options: [
            { value: 'panorama', label: '全景拼接' },
            { value: 'scans', label: '扫描拼接' }
          ],
          default: 'panorama'
        },
        'save_video': {
          type: 'checkbox',
          default: true,
          label: '保存视频文件'
        }
      }
      return configs[param] || { type: 'text', default: '' }
    }

    const config = getParameterConfig(paramName)
    const currentValue = value || config.default

    // 更多调试信息
    console.log('Parameter config for', paramName, ':', config)
    console.log('Current value:', currentValue)

    // 渲染滑块组件
    if (config.type === 'range') {
      return (
        <div className="parameter-range">
          <div className="range-header">
            <span className="range-label">{paramName}</span>
            <span className="range-value">{currentValue}{config.unit}</span>
          </div>
          <input
            type="range"
            className="range-slider"
            min={config.min}
            max={config.max}
            step={config.step}
            value={currentValue}
            onChange={(e) => handleParameterChange(paramName, parseFloat(e.target.value))}
          />
          <div className="range-limits">
            <span>{config.min}{config.unit}</span>
            <span>{config.max}{config.unit}</span>
          </div>
        </div>
      )
    }

    // 渲染下拉选择组件
    if (config.type === 'select') {
      return (
        <div className="parameter-select">
          <label className="select-label">{paramName}:</label>
          <select
            className="select"
            value={currentValue}
            onChange={(e) => handleParameterChange(paramName, e.target.value)}
          >
            {config.options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      )
    }

    // 渲染复选框组件
    if (config.type === 'checkbox') {
      return (
        <div className="parameter-checkbox">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={currentValue}
              onChange={(e) => handleParameterChange(paramName, e.target.checked)}
            />
            <span className="checkbox-text">{config.label}</span>
          </label>
        </div>
      )
    }

    // 默认文本输入
    return (
      <input
        type="text"
        className="input"
        value={currentValue}
        onChange={(e) => handleParameterChange(paramName, e.target.value)}
        placeholder={`输入${paramName}`}
      />
    )
  }

  return (
    <div className="operation-panel">
      <div className="card">
        <h2 className="section-title">操作选择</h2>
        
        {/* 操作选择 */}
        <div className="operation-selector">
          <label className="operation-label">选择操作:</label>
          <select
            className="select"
            value={selectedOperation}
            onChange={(e) => onOperationSelect(e.target.value)}
            disabled={!hasFile}
          >
            <option value="">请选择操作</option>
            {currentOperations.map(op => (
              <option key={op.id} value={op.id}>
                {op.name}
              </option>
            ))}
          </select>
        </div>

        {/* 参数设置 */}
        {selectedOp && selectedOp.parameters && selectedOp.parameters.length > 0 && (
          <div className="parameters-section">
            <h3 className="parameters-title">参数设置</h3>
            <div className="parameters-grid">
              {selectedOp.parameters.map(param => (
                <div key={param} className="parameter-item">
                  <label className="parameter-label">
                    {param}:
                  </label>
                  {renderParameterInput(param)}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="action-buttons">
          <button
            className="btn btn-primary"
            onClick={onProcess}
            disabled={!hasFile || !selectedOperation || isProcessing}
          >
            {isProcessing ? (
              <>
                <Loader2 className="btn-icon spinning" />
                处理中...
              </>
            ) : (
              <>
                <Play className="btn-icon" />
                开始处理
              </>
            )}
          </button>
          
          <button
            className="btn btn-secondary"
            onClick={onClear}
            disabled={isProcessing}
          >
            <RotateCcw className="btn-icon" />
            清除选择
          </button>
        </div>
      </div>
    </div>
  )
}

export default OperationPanel
// Force recompile
