"""
视频尺寸调整任务
"""
import os
import logging
from celery import current_task

from tasks.opencv_ffmpeg_processor import OpenCVFFmpegProcessor, create_opencv_processor

logger = logging.getLogger(__name__)


def create_celery_progress_callback(task_instance):
    """创建Celery进度回调函数"""
    def progress_callback(current, total, status="Processing"):
        task_instance.update_state(
            state='PROGRESS',
            meta={'current': current, 'total': total, 'status': status}
        )
    return progress_callback


def resize_video_task(self, file_path, parameters):
    """视频缩放处理"""
    try:
        progress_callback = create_celery_progress_callback(self)
        
        # 获取缩放参数
        scale_mode = parameters.get('scale_mode', 'custom')
        
        with OpenCVFFmpegProcessor(file_path, preserve_audio=True) as processor:
            # 获取视频信息
            video_info = processor.get_video_info()
            original_width, original_height = video_info['size']
            
            if scale_mode == 'ratio':
                # 按比例缩放
                scale_ratio = parameters.get('scale_ratio', 1.0)
                target_width = int(original_width * scale_ratio)
                target_height = int(original_height * scale_ratio)
            else:
                # 指定尺寸缩放
                target_width = parameters.get('width', original_width)
                target_height = parameters.get('height', original_height)
                
                # 如果只指定了一个维度，保持宽高比
                if 'width' in parameters and 'height' not in parameters:
                    aspect_ratio = original_height / original_width
                    target_height = int(target_width * aspect_ratio)
                elif 'height' in parameters and 'width' not in parameters:
                    aspect_ratio = original_width / original_height
                    target_width = int(target_height * aspect_ratio)
            
            # 创建缩放处理器
            opencv_processor = create_opencv_processor('resize', width=target_width, height=target_height)
            
            result = processor.process_frames_with_opencv(
                opencv_processor=opencv_processor,
                output_prefix='resized',
                progress_callback=progress_callback
            )
            
            # 添加缩放信息到结果
            result.update({
                'original_size': f'{original_width}x{original_height}',
                'new_size': f'{target_width}x{target_height}',
                'scale_mode': scale_mode
            })
            
            self.update_state(state='SUCCESS', meta=result)
            return result
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Resize video task failed: {error_msg}")
        self.update_state(state='FAILURE', meta={'error': error_msg})
        raise Exception(f"Video resize processing failed: {error_msg}")