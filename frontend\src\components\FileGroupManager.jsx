import React, { useState, useEffect } from 'react'
import { Plus, Play, Folder, AlertTriangle } from 'lucide-react'
import FileGroup from './FileGroup'
import './FileGroupManager.css'

const FileGroupManager = ({ 
  operations = {}, 
  onBatchProcess, 
  isProcessing = false,
  onLog 
}) => {
  const [fileGroups, setFileGroups] = useState([])

  // 添加新的文件组
  const addFileGroup = () => {
    const newGroup = {
      id: `group-${Date.now()}`,
      name: `文件组 ${fileGroups.length + 1}`,
      operation: '',
      requiredFileCount: 1,
      files: [{ id: `group-${Date.now()}-file-0`, file: null, slot: 0 }],
      parameters: {},
      isComplete: false
    }
    setFileGroups([...fileGroups, newGroup])
    if (onLog) {
      onLog(`添加新文件组: ${newGroup.name}`, 'info')
    }
  }

  // 更新文件组
  const updateFileGroup = (updatedGroup) => {
    setFileGroups(groups => 
      groups.map(group => 
        group.id === updatedGroup.id ? updatedGroup : group
      )
    )
  }

  // 删除文件组
  const removeFileGroup = (groupId) => {
    const group = fileGroups.find(g => g.id === groupId)
    setFileGroups(groups => groups.filter(group => group.id !== groupId))
    if (onLog && group) {
      onLog(`删除文件组: ${group.name}`, 'info')
    }
  }

  // 检查是否有完整的文件组
  const getCompleteGroups = () => {
    return fileGroups.filter(group => {
      if (!group.operation) return false
      const requiredCount = getRequiredFileCount(group.operation)
      const validFiles = group.files.filter(f =>
        f &&
        f.file &&
        f.file instanceof File &&
        f.file.size > 0 &&
        f.file.name
      )
      return validFiles.length >= requiredCount
    })
  }

  // 获取操作的文件数量要求
  const getRequiredFileCount = (operation) => {
    const twoFileOperations = ['image_fusion', 'image_stitching', 'texture_transfer']
    return twoFileOperations.includes(operation) ? 2 : 1
  }

  // 开始批处理
  const handleBatchProcess = () => {
    const completeGroups = getCompleteGroups()
    
    if (completeGroups.length === 0) {
      if (onLog) {
        onLog('没有完整的文件组可以处理', 'error')
      }
      return
    }

    // 转换文件组为批处理任务格式
    const batchTasks = completeGroups.map(group => ({
      id: group.id,
      name: group.name,
      fileType: 'image', // 目前只支持图像
      files: group.files
        .filter(f =>
          f &&
          f.file &&
          f.file instanceof File &&
          f.file.size > 0 &&
          f.file.name
        )
        .map(f => f.file),
      operation: group.operation,
      parameters: group.parameters || {},
      isFileGroup: true,
      requiredFileCount: group.requiredFileCount
    }))

    if (onBatchProcess) {
      onBatchProcess(batchTasks)
    }
  }

  // 获取统计信息
  const getStats = () => {
    const total = fileGroups.length
    const complete = getCompleteGroups().length
    const incomplete = total - complete
    
    return { total, complete, incomplete }
  }

  const stats = getStats()
  const hasCompleteGroups = stats.complete > 0

  return (
    <div className="file-group-manager">
      <div className="manager-header">
        <div className="header-info">
          <h2 className="manager-title">文件组批处理</h2>
          <div className="stats-info">
            <span className="stat-item">
              总计: <strong>{stats.total}</strong>
            </span>
            <span className="stat-item complete">
              完成: <strong>{stats.complete}</strong>
            </span>
            {stats.incomplete > 0 && (
              <span className="stat-item incomplete">
                待完成: <strong>{stats.incomplete}</strong>
              </span>
            )}
          </div>
        </div>
        <button 
          className="btn btn-primary"
          onClick={addFileGroup}
          disabled={isProcessing}
        >
          <Plus className="btn-icon" />
          添加文件组
        </button>
      </div>

      {fileGroups.length === 0 ? (
        <div className="empty-state">
          <Folder className="empty-icon" />
          <p>暂无文件组</p>
          <p className="empty-description">
            点击"添加文件组"开始创建文件组进行批处理
          </p>
          <div className="help-text">
            <AlertTriangle className="help-icon" />
            <span>文件组适用于需要多个文件的操作，如图像拼接、纹理迁移等</span>
          </div>
        </div>
      ) : (
        <div className="file-groups-list">
          {fileGroups.map(group => (
            <FileGroup
              key={group.id}
              group={group}
              onUpdate={updateFileGroup}
              onRemove={removeFileGroup}
              operations={operations}
              disabled={isProcessing}
            />
          ))}
        </div>
      )}

      {fileGroups.length > 0 && (
        <div className="manager-actions">
          <button
            className="btn btn-success"
            onClick={handleBatchProcess}
            disabled={isProcessing || !hasCompleteGroups}
          >
            <Play className="btn-icon" />
            {isProcessing ? '处理中...' : `开始批处理 (${stats.complete}个文件组)`}
          </button>
          
          {!hasCompleteGroups && fileGroups.length > 0 && (
            <div className="warning-message">
              <AlertTriangle className="warning-icon" />
              <span>请完成文件组配置后再开始批处理</span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default FileGroupManager
