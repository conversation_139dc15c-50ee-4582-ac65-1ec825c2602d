"""
缓存管理工具
用于管理视频缩略图和其他文件的缓存
"""
import os
import time
import json
import logging
import hashlib
from typing import Dict, List, Optional, Tuple
from config import THUMBNAIL_FOLDER

logger = logging.getLogger(__name__)


class ThumbnailCacheManager:
    """视频缩略图缓存管理器"""
    
    def __init__(self, cache_dir: str = THUMBNAIL_FOLDER):
        self.cache_dir = cache_dir
        self.metadata_file = os.path.join(cache_dir, '.cache_metadata.json')
        self._ensure_cache_dir()
        
    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        os.makedirs(self.cache_dir, exist_ok=True)
        
    def _load_metadata(self) -> Dict:
        """加载缓存元数据"""
        try:
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load cache metadata: {e}")
        return {}
    
    def _save_metadata(self, metadata: Dict):
        """保存缓存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save cache metadata: {e}")
    
    def generate_cache_key(self, video_path: str, size: Tuple[int, int], 
                          frame_number: int = 0) -> str:
        """
        生成缓存键
        
        Args:
            video_path: 视频文件路径
            size: 缩略图尺寸
            frame_number: 帧号
            
        Returns:
            str: 缓存键
        """
        # 获取文件的修改时间和大小作为版本标识
        try:
            stat = os.stat(video_path)
            file_info = f"{stat.st_mtime}_{stat.st_size}"
        except OSError:
            file_info = "unknown"
        
        # 生成唯一标识
        content = f"{video_path}_{size[0]}x{size[1]}_{frame_number}_{file_info}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def get_cache_filename(self, cache_key: str, video_path: str, 
                          size: Tuple[int, int]) -> str:
        """
        生成缓存文件名
        
        Args:
            cache_key: 缓存键
            video_path: 原视频路径
            size: 缩略图尺寸
            
        Returns:
            str: 缓存文件名
        """
        base_name = os.path.splitext(os.path.basename(video_path))[0]
        return f"thumb_{base_name}_{cache_key[:8]}_{size[0]}x{size[1]}.jpg"
    
    def get_cached_thumbnail(self, video_path: str, size: Tuple[int, int], 
                           frame_number: int = 0) -> Optional[str]:
        """
        获取缓存的缩略图路径
        
        Args:
            video_path: 视频文件路径
            size: 缩略图尺寸
            frame_number: 帧号
            
        Returns:
            Optional[str]: 缓存文件路径，如果不存在返回None
        """
        try:
            cache_key = self.generate_cache_key(video_path, size, frame_number)
            cache_filename = self.get_cache_filename(cache_key, video_path, size)
            cache_path = os.path.join(self.cache_dir, cache_filename)
            
            if os.path.exists(cache_path):
                # 更新访问时间
                self._update_access_time(cache_key)
                return cache_path
                
        except Exception as e:
            logger.error(f"Failed to get cached thumbnail: {e}")
        
        return None
    
    def cache_thumbnail(self, video_path: str, thumbnail_path: str, 
                       size: Tuple[int, int], frame_number: int = 0) -> bool:
        """
        缓存缩略图
        
        Args:
            video_path: 原视频路径
            thumbnail_path: 缩略图文件路径
            size: 缩略图尺寸
            frame_number: 帧号
            
        Returns:
            bool: 是否成功缓存
        """
        try:
            cache_key = self.generate_cache_key(video_path, size, frame_number)
            
            # 更新元数据
            metadata = self._load_metadata()
            metadata[cache_key] = {
                'video_path': video_path,
                'thumbnail_path': thumbnail_path,
                'size': size,
                'frame_number': frame_number,
                'created_time': time.time(),
                'last_access': time.time(),
                'access_count': 1
            }
            self._save_metadata(metadata)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache thumbnail: {e}")
            return False
    
    def _update_access_time(self, cache_key: str):
        """更新缓存访问时间"""
        try:
            metadata = self._load_metadata()
            if cache_key in metadata:
                metadata[cache_key]['last_access'] = time.time()
                metadata[cache_key]['access_count'] = metadata[cache_key].get('access_count', 0) + 1
                self._save_metadata(metadata)
        except Exception as e:
            logger.error(f"Failed to update access time: {e}")
    
    def cleanup_expired_cache(self, max_age_days: int = 7, 
                            max_unused_days: int = 3) -> Dict[str, int]:
        """
        清理过期缓存
        
        Args:
            max_age_days: 最大保留天数
            max_unused_days: 最大未使用天数
            
        Returns:
            Dict[str, int]: 清理统计信息
        """
        stats = {
            'total_files': 0,
            'expired_files': 0,
            'unused_files': 0,
            'orphaned_files': 0,
            'errors': 0
        }
        
        try:
            metadata = self._load_metadata()
            current_time = time.time()
            max_age_seconds = max_age_days * 24 * 60 * 60
            max_unused_seconds = max_unused_days * 24 * 60 * 60
            
            # 获取所有缓存文件
            cache_files = set()
            if os.path.exists(self.cache_dir):
                for filename in os.listdir(self.cache_dir):
                    if filename.startswith('thumb_') and filename.endswith('.jpg'):
                        cache_files.add(filename)
                        stats['total_files'] += 1
            
            # 清理过期和未使用的缓存
            keys_to_remove = []
            for cache_key, info in metadata.items():
                try:
                    thumbnail_path = info.get('thumbnail_path', '')
                    filename = os.path.basename(thumbnail_path)
                    
                    # 检查文件是否存在
                    if filename in cache_files:
                        cache_files.remove(filename)
                        
                        created_time = info.get('created_time', 0)
                        last_access = info.get('last_access', created_time)
                        
                        # 检查是否过期
                        if current_time - created_time > max_age_seconds:
                            self._remove_cache_file(thumbnail_path)
                            keys_to_remove.append(cache_key)
                            stats['expired_files'] += 1
                            continue
                        
                        # 检查是否长时间未使用
                        if current_time - last_access > max_unused_seconds:
                            self._remove_cache_file(thumbnail_path)
                            keys_to_remove.append(cache_key)
                            stats['unused_files'] += 1
                            continue
                    else:
                        # 元数据中的文件不存在
                        keys_to_remove.append(cache_key)
                        
                except Exception as e:
                    logger.error(f"Error processing cache key {cache_key}: {e}")
                    stats['errors'] += 1
            
            # 清理孤立文件（没有元数据的缓存文件）
            for orphaned_file in cache_files:
                try:
                    orphaned_path = os.path.join(self.cache_dir, orphaned_file)
                    self._remove_cache_file(orphaned_path)
                    stats['orphaned_files'] += 1
                except Exception as e:
                    logger.error(f"Error removing orphaned file {orphaned_file}: {e}")
                    stats['errors'] += 1
            
            # 更新元数据
            for key in keys_to_remove:
                metadata.pop(key, None)
            self._save_metadata(metadata)
            
            logger.info(f"Cache cleanup completed: {stats}")
            
        except Exception as e:
            logger.error(f"Cache cleanup failed: {e}")
            stats['errors'] += 1
        
        return stats
    
    def _remove_cache_file(self, file_path: str):
        """安全删除缓存文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.debug(f"Removed cache file: {file_path}")
        except Exception as e:
            logger.error(f"Failed to remove cache file {file_path}: {e}")
            raise
    
    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        try:
            metadata = self._load_metadata()
            current_time = time.time()
            
            stats = {
                'total_cached_items': len(metadata),
                'cache_dir_size_mb': 0,
                'oldest_cache_age_days': 0,
                'most_accessed_item': None,
                'cache_hit_rate': 0
            }
            
            # 计算缓存目录大小
            if os.path.exists(self.cache_dir):
                total_size = 0
                for filename in os.listdir(self.cache_dir):
                    file_path = os.path.join(self.cache_dir, filename)
                    if os.path.isfile(file_path):
                        total_size += os.path.getsize(file_path)
                stats['cache_dir_size_mb'] = round(total_size / (1024 * 1024), 2)
            
            # 分析缓存项
            if metadata:
                oldest_time = min(info.get('created_time', current_time) for info in metadata.values())
                stats['oldest_cache_age_days'] = round((current_time - oldest_time) / (24 * 60 * 60), 1)
                
                # 找到访问次数最多的项
                most_accessed = max(metadata.items(), 
                                  key=lambda x: x[1].get('access_count', 0))
                stats['most_accessed_item'] = {
                    'video_path': most_accessed[1].get('video_path'),
                    'access_count': most_accessed[1].get('access_count', 0)
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {'error': str(e)}


# 全局缓存管理器实例
thumbnail_cache = ThumbnailCacheManager()
