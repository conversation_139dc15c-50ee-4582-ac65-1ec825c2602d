/* 文件组样式 - 复用web_frontend/index.html中的样式 */
.file-group {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.file-group:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.file-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.group-info {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.group-name-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  min-width: 150px;
  transition: all 0.2s ease;
}

.group-name-input:focus {
  outline: none;
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.group-name-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.group-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-complete {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-incomplete {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-icon {
  width: 14px;
  height: 14px;
}

.remove-group-btn {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 6px;
  padding: 6px;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-group-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
}

.remove-group-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.remove-icon {
  width: 16px;
  height: 16px;
}

.group-config {
  margin-bottom: 15px;
}

.operation-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.config-label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  min-width: 70px;
}

.operation-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  color: white;
  font-size: 13px;
  min-width: 180px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.operation-select:focus {
  outline: none;
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.operation-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.operation-select option {
  background: #2a5298;
  color: white;
}

.file-slots {
  display: flex;
  gap: 20px;
  justify-content: space-between;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-group {
    padding: 12px;
  }

  .file-group-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .group-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    width: 100%;
  }

  .group-name-input {
    width: 100%;
    min-width: auto;
  }

  .operation-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
  }

  .operation-select {
    width: 100%;
    min-width: auto;
  }

  .file-slots {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .file-group {
    padding: 10px;
    margin-bottom: 12px;
  }

  .file-group-header {
    font-size: 13px;
  }

  .group-name-input {
    font-size: 13px;
    padding: 6px 10px;
  }

  .operation-select {
    font-size: 12px;
    padding: 6px 10px;
  }

  .config-label {
    font-size: 12px;
  }
}
