const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// 向渲染进程暴露安全的API
contextBridge.exposeInMainWorld('electronAPI', {
    // 获取应用版本
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),

    // 平台信息
    platform: process.platform,

    // 应用信息
    isElectron: true,

    // 日志功能
    log: (message) => {
        console.log('[Electron]', message);
    },

    // 错误报告
    reportError: (error) => {
        console.error('[Electron Error]', error);
    }
});

// Electron 环境标识
console.log('Preload script loaded in Electron environment');
