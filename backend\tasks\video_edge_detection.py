"""
视频边缘检测任务
"""
import os
import logging
from celery import current_task

from tasks.opencv_ffmpeg_processor import OpenCVFFmpegProcessor, create_opencv_processor

logger = logging.getLogger(__name__)


def create_celery_progress_callback(task_instance):
    """创建Celery进度回调函数"""
    def progress_callback(current, total, status="Processing"):
        task_instance.update_state(
            state='PROGRESS',
            meta={'current': current, 'total': total, 'status': status}
        )
    return progress_callback


def edge_detection_video_task(self, file_path, parameters):
    """视频边缘检测处理"""
    try:
        progress_callback = create_celery_progress_callback(self)
        
        # 获取边缘检测参数
        low_threshold = parameters.get('low_threshold', 50)
        high_threshold = parameters.get('high_threshold', 150)
        edge_type = parameters.get('edge_type', 'canny')
        
        # 创建边缘检测处理器
        opencv_processor = create_opencv_processor(
            'edge_detection', 
            low_threshold=low_threshold, 
            high_threshold=high_threshold, 
            edge_type=edge_type
        )
        
        with OpenCVFFmpegProcessor(file_path, preserve_audio=True) as processor:
            result = processor.process_frames_with_opencv(
                opencv_processor=opencv_processor,
                output_prefix=f'{edge_type}_edges',
                progress_callback=progress_callback
            )
            
            # 添加边缘检测参数到结果
            result.update({
                'edge_type': edge_type,
                'low_threshold': low_threshold,
                'high_threshold': high_threshold
            })
            
            self.update_state(state='SUCCESS', meta=result)
            return result
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Edge detection video task failed: {error_msg}")
        self.update_state(state='FAILURE', meta={'error': error_msg})
        raise Exception(f"Video edge detection processing failed: {error_msg}")