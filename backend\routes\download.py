"""
文件下载路由
"""
import os
import zipfile
import tempfile
from flask import Blueprint, send_file, abort, request, jsonify
from config import OUTPUT_FOLDER
from utils.validation import validate_file_path

download_bp = Blueprint('download', __name__)


@download_bp.route('/api/download/<filename>', methods=['GET'])
def download_file(filename):
    """单文件下载接口"""
    try:
        # 构造文件路径
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        file_path = os.path.abspath(file_path)
        
        # 验证文件路径安全性
        abs_output_folder = os.path.abspath(OUTPUT_FOLDER)
        if not validate_file_path(file_path, abs_output_folder):
            abort(403)  # Forbidden
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            abort(404)  # Not Found
        
        # 发送文件
        return send_file(file_path, as_attachment=True, download_name=filename)
    
    except Exception as e:
        print(f"Download error: {e}")
        abort(500)


@download_bp.route('/api/download', methods=['POST'])
def download_batch():
    """批量下载接口"""
    try:
        data = request.get_json()
        if not data or 'filenames' not in data:
            return jsonify({'error': 'No filenames provided'}), 400
        
        filenames = data['filenames']
        if not isinstance(filenames, list) or not filenames:
            return jsonify({'error': 'filenames must be a non-empty list'}), 400
        
        # 创建临时ZIP文件
        temp_zip = tempfile.NamedTemporaryFile(delete=False, suffix='.zip')
        
        with zipfile.ZipFile(temp_zip.name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for filename in filenames:
                file_path = os.path.join(OUTPUT_FOLDER, filename)
                file_path = os.path.abspath(file_path)
                
                # 验证文件路径安全性
                abs_output_folder = os.path.abspath(OUTPUT_FOLDER)
                if not validate_file_path(file_path, abs_output_folder):
                    continue  # 跳过不安全的文件
                
                # 检查文件是否存在
                if os.path.exists(file_path):
                    zipf.write(file_path, filename)
        
        # 发送ZIP文件
        return send_file(
            temp_zip.name,
            as_attachment=True,
            download_name='batch_download.zip',
            mimetype='application/zip'
        )
    
    except Exception as e:
        print(f"Batch download error: {e}")
        return jsonify({'error': 'Batch download failed'}), 500
