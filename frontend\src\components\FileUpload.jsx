import React from 'react'
import { Upload, File, X } from 'lucide-react'
import './FileUpload.css'

const FileUpload = ({ 
  selectedFile, 
  onFileSelect, 
  secondFile, 
  onSecondFileSelect, 
  currentType, 
  onTypeChange 
}) => {
  const handleFileSelect = (event) => {
    const file = event.target.files[0]
    if (file) {
      onFileSelect(file)
    }
  }

  const handleSecondFileSelect = (event) => {
    const file = event.target.files[0]
    if (file) {
      onSecondFileSelect(file)
    }
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileType = (file) => {
    const extension = file.name.split('.').pop().toLowerCase()
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
      return 'image'
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(extension)) {
      return 'video'
    } else if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(extension)) {
      return 'audio'
    } else if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(extension)) {
      return 'document'
    }
    return 'other'
  }

  return (
    <div className="file-upload-section">
      <div className="card">
        <h2 className="section-title">文件上传</h2>
        
        {/* 文件类型选择 */}
        <div className="type-selector">
          <label className="type-label">文件类型:</label>
          <select
            className="select"
            value={currentType}
            onChange={(e) => onTypeChange(e.target.value)}
          >
            <option value="image">图片</option>
            <option value="video">视频</option>
          </select>
        </div>

        {/* 主文件上传 */}
        <div className="upload-area">
          <input
            type="file"
            id="fileInput"
            className="file-input"
            onChange={handleFileSelect}
            accept={currentType === 'image' ? 'image/*' : 
                   currentType === 'video' ? 'video/*' :
                   currentType === 'audio' ? 'audio/*' : '*'}
          />
          <label htmlFor="fileInput" className="upload-label">
            <Upload className="upload-icon" />
            <span className="upload-text">
              {selectedFile ? '更换文件' : '选择文件或拖拽到此处'}
            </span>
          </label>
        </div>

        {/* 已选择的文件信息 */}
        {selectedFile && (
          <div className="file-info">
            <div className="file-details">
              <File className="file-icon" />
              <div className="file-meta">
                <div className="file-name">{selectedFile.name}</div>
                <div className="file-stats">
                  <span className="file-size">{formatFileSize(selectedFile.size)}</span>
                  <span className="file-type">{getFileType(selectedFile)}</span>
                </div>
              </div>
            </div>
            <button 
              className="remove-btn"
              onClick={() => onFileSelect(null)}
              title="移除文件"
            >
              <X className="remove-icon" />
            </button>
          </div>
        )}

        {/* 第二文件上传（某些操作需要） */}
        {selectedFile && (
          <div className="second-file-section">
            <h3 className="second-file-title">第二个文件（可选）</h3>
            <div className="upload-area secondary">
              <input
                type="file"
                id="secondFileInput"
                className="file-input"
                onChange={handleSecondFileSelect}
              />
              <label htmlFor="secondFileInput" className="upload-label">
                <Upload className="upload-icon" />
                <span className="upload-text">
                  {secondFile ? '更换第二个文件' : '选择第二个文件'}
                </span>
              </label>
            </div>

            {secondFile && (
              <div className="file-info">
                <div className="file-details">
                  <File className="file-icon" />
                  <div className="file-meta">
                    <div className="file-name">{secondFile.name}</div>
                    <div className="file-stats">
                      <span className="file-size">{formatFileSize(secondFile.size)}</span>
                      <span className="file-type">{getFileType(secondFile)}</span>
                    </div>
                  </div>
                </div>
                <button 
                  className="remove-btn"
                  onClick={() => onSecondFileSelect(null)}
                  title="移除文件"
                >
                  <X className="remove-icon" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default FileUpload
