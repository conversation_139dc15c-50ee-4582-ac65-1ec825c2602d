"""
系统功能路由
"""
from flask import Blueprint, jsonify

system_bp = Blueprint('system', __name__)


@system_bp.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'message': 'File Processing API is running',
        'architecture': 'Modular processing system'
    })


@system_bp.route('/api/operations', methods=['GET'])
def get_available_operations():
    """获取可用的操作列表"""
    operations = {
        'image': [
            # 基础图像处理
            {'id': 'sharpen', 'name': '图像锐化', 'parameters': ['intensity'], 'category': 'basic'},
            {'id': 'gamma_correction', 'name': '伽马校正', 'parameters': ['gamma'], 'category': 'basic'},
            {'id': 'grayscale', 'name': '灰度转换', 'parameters': [], 'category': 'basic'},
            {'id': 'canny_edge', 'name': '边缘检测', 'parameters': ['low_threshold', 'high_threshold'], 'category': 'basic'},

            # 高级图像处理
            {'id': 'image_fusion', 'name': '图像融合', 'parameters': ['weight1', 'weight2'], 'category': 'advanced'},
            {'id': 'beauty_enhancement', 'name': '美颜处理', 'parameters': ['slimming_strength', 'smoothing_strength'], 'category': 'advanced'},
            {'id': 'image_stitching', 'name': '图像拼接', 'parameters': ['mode'], 'category': 'advanced'},
            {'id': 'texture_transfer', 'name': '纹理迁移', 'parameters': ['block_size', 'alpha_start', 'alpha_end'], 'category': 'advanced'},
        ],
        'video': [
            # 基础视频处理
            {'id': 'resize', 'name': '视频缩放', 'parameters': ['width', 'height', 'scale_mode', 'scale_ratio'], 'category': 'basic'},
            {'id': 'grayscale', 'name': '灰度转换', 'parameters': [], 'category': 'basic'},
            {'id': 'binary', 'name': '二值化', 'parameters': ['threshold', 'max_value', 'threshold_type'], 'category': 'basic'},
            {'id': 'blur', 'name': '模糊处理', 'parameters': ['filter_type', 'kernel_size', 'sigma'], 'category': 'basic'},
            {'id': 'edge_detection', 'name': '边缘检测', 'parameters': ['low_threshold', 'high_threshold', 'edge_type'], 'category': 'basic'},
            {'id': 'transform', 'name': '几何变换', 'parameters': ['transform_type', 'angle'], 'category': 'basic'},

            # 高级视频处理
            {'id': 'extract_frame', 'name': '提取帧', 'parameters': ['frame_number'], 'category': 'advanced'},
        ]
    }

    return jsonify({
        'supported_types': list(operations.keys()),
        'operations': operations
    })
