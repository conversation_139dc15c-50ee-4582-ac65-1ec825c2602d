.task-progress-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-progress-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.task-progress-card.processing {
  border-left: 4px solid #3b82f6;
}

.task-progress-card.success {
  border-left: 4px solid #10b981;
}

.task-progress-card.error {
  border-left: 4px solid #ef4444;
}

.task-progress-card.pending {
  border-left: 4px solid var(--border-color);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 16px;
}

.elapsed-time {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: 6px;
}

.task-progress {
  margin-bottom: 20px;
}

.task-stage {
  margin-bottom: 16px;
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 12px;
}

.task-summary {
  border-top: 1px solid var(--border-color);
  padding-top: 16px;
}

.summary-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
  background: var(--bg-secondary);
}

.stat.success {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.stat.error {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.stat.processing {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.stat.total {
  color: var(--text-secondary);
  background: var(--bg-secondary);
}

/* 状态图标样式 */
.status-icon {
  flex-shrink: 0;
}

.status-icon.success {
  color: #10b981;
}

.status-icon.error {
  color: #ef4444;
}

.status-icon.processing {
  color: #3b82f6;
}

.status-icon.pending {
  color: var(--text-tertiary);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-progress-card {
    padding: 16px;
    margin-bottom: 12px;
  }
  
  .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .task-title {
    gap: 8px;
  }
  
  .task-name {
    font-size: 15px;
  }
  
  .task-meta {
    gap: 12px;
  }
  
  .elapsed-time {
    font-size: 11px;
  }
  
  .task-stage {
    padding: 8px;
  }
  
  .summary-stats {
    gap: 12px;
  }
  
  .stat {
    font-size: 11px;
    padding: 3px 6px;
  }
}
