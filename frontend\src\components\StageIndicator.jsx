import React from 'react'
import { Upload, Cog, CheckCircle, AlertCircle, Clock } from 'lucide-react'
import './StageIndicator.css'

const StageIndicator = ({ currentStage = 'pending', status = 'processing' }) => {
  const stages = [
    {
      key: 'upload',
      label: '上传中',
      icon: Upload,
      description: '文件正在上传到服务器'
    },
    {
      key: 'processing',
      label: '处理中',
      icon: Cog,
      description: '正在执行处理任务'
    },
    {
      key: 'completed',
      label: '已完成',
      icon: status === 'error' ? AlertCircle : CheckCircle,
      description: status === 'error' ? '处理失败' : '任务处理完成'
    }
  ]

  const getStageStatus = (stageKey) => {
    const stageIndex = stages.findIndex(s => s.key === stageKey)
    const currentIndex = stages.findIndex(s => s.key === currentStage)
    
    if (status === 'error' && stageKey === 'completed') {
      return 'error'
    }
    
    if (stageIndex < currentIndex) {
      return 'completed'
    } else if (stageIndex === currentIndex) {
      return 'active'
    } else {
      return 'pending'
    }
  }

  return (
    <div className="stage-indicator">
      <div className="stages-container">
        {stages.map((stage, index) => {
          const stageStatus = getStageStatus(stage.key)
          const Icon = stage.icon
          
          return (
            <div key={stage.key} className="stage-wrapper">
              <div className={`stage-item ${stageStatus}`}>
                <div className="stage-icon">
                  <Icon size={16} />
                </div>
                <div className="stage-content">
                  <div className="stage-label">{stage.label}</div>
                  <div className="stage-description">{stage.description}</div>
                </div>
              </div>
              
              {index < stages.length - 1 && (
                <div className={`stage-connector ${stageStatus === 'completed' ? 'completed' : ''}`} />
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default StageIndicator
