/**
 * 视频处理API工具函数
 * 支持新的v2 API（音频保留）和旧版API的兼容性
 */

/**
 * 检测文件类型是否为视频
 * @param {string} filename - 文件名
 * @returns {boolean} 是否为视频文件
 */
export const isVideoFile = (filename) => {
  const videoExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
  return videoExtensions.includes(extension)
}

/**
 * 获取支持音频保留的视频操作列表
 * @returns {string[]} 支持音频保留的操作列表
 */
export const getAudioPreservingOperations = () => {
  return [
    'grayscale',
    'resize', 
    'face_detection',
    'binary',
    'blur',
    'edge_detection'
  ]
}

/**
 * 检查操作是否支持音频保留
 * @param {string} operation - 操作名称
 * @returns {boolean} 是否支持音频保留
 */
export const supportsAudioPreservation = (operation) => {
  return getAudioPreservingOperations().includes(operation)
}

/**
 * 处理视频文件
 * @param {string} apiBase - API基础URL
 * @param {string} filePath - 文件路径
 * @param {string} operation - 操作类型
 * @param {Object} parameters - 操作参数
 * @returns {Promise<Object>} API响应
 */
export const processVideo = async (apiBase, filePath, operation, parameters = {}) => {
  try {
    // 构造批量处理数据格式
    const fileInfo = typeof filePath === 'string'
      ? { stored_filename: filePath.split('/').pop(), filename: filePath.split('/').pop() }
      : filePath

    const data = {
      files: [fileInfo],
      operation: operation,
      parameters: parameters
    }

    console.log(`🎬 Processing video with batch API:`, {
      operation,
      supportsAudio: supportsAudioPreservation(operation)
    })

    const response = await fetch(`${apiBase}/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data),
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()

    // 添加音频保留信息到结果中
    result.audio_preserved = supportsAudioPreservation(operation)

    return result
  } catch (error) {
    console.error('processVideo error:', error)
    throw error
  }
}

/**
 * 批量处理视频文件
 * @param {string} apiBase - API基础URL
 * @param {Array} videoTasks - 视频任务列表
 * @param {Function} onProgress - 进度回调
 * @returns {Promise<Array>} 处理结果列表
 */
export const batchProcessVideos = async (apiBase, videoTasks, onProgress = null) => {
  const results = []
  
  for (let i = 0; i < videoTasks.length; i++) {
    const task = videoTasks[i]
    
    if (onProgress) {
      onProgress(i, videoTasks.length, `Processing ${task.operation}...`)
    }
    
    try {
      const result = await processVideo(
        apiBase,
        task.filePath,
        task.operation,
        task.parameters
      )
      
      results.push({
        ...result,
        taskIndex: i,
        taskName: task.name || task.operation
      })
      
    } catch (error) {
      console.error(`❌ Task ${i} failed:`, error)
      results.push({
        status: 'error',
        error: error.message,
        taskIndex: i,
        taskName: task.name || task.operation
      })
    }
  }
  
  if (onProgress) {
    onProgress(videoTasks.length, videoTasks.length, 'All tasks completed')
  }
  
  return results
}

/**
 * 获取视频处理状态
 * @param {string} apiBase - API基础URL
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务状态
 */
export const getVideoTaskStatus = async (apiBase, taskId) => {
  const response = await fetch(`${apiBase}/process/status`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ task_ids: [taskId] }),
    mode: 'cors'
  })

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }

  const result = await response.json()
  // 返回第一个任务的状态
  return result.results && result.results[0] ? result.results[0] : { status: 'PENDING' }
}

/**
 * 轮询任务状态直到完成
 * @param {string} apiBase - API基础URL
 * @param {string} taskId - 任务ID
 * @param {Function} onProgress - 进度回调
 * @param {number} pollInterval - 轮询间隔（毫秒）
 * @param {number} maxAttempts - 最大尝试次数
 * @returns {Promise<Object>} 最终任务状态
 */
export const pollVideoTaskStatus = async (
  apiBase, 
  taskId, 
  onProgress = null, 
  pollInterval = 1000, 
  maxAttempts = 300
) => {
  let attempts = 0
  
  while (attempts < maxAttempts) {
    try {
      const status = await getVideoTaskStatus(apiBase, taskId)
      
      if (onProgress) {
        onProgress(status)
      }
      
      // 任务完成
      if (status.state === 'SUCCESS') {
        return status
      }
      
      // 任务失败
      if (status.state === 'FAILURE') {
        throw new Error(status.result?.error || 'Task failed')
      }
      
      // 继续等待
      await new Promise(resolve => setTimeout(resolve, pollInterval))
      attempts++
      
    } catch (error) {
      console.error('❌ Error polling task status:', error)
      throw error
    }
  }
  
  throw new Error('Task polling timeout')
}

/**
 * 创建视频处理任务配置
 * @param {string} filePath - 文件路径
 * @param {string} operation - 操作类型
 * @param {Object} parameters - 参数
 * @param {string} name - 任务名称
 * @returns {Object} 任务配置
 */
export const createVideoTask = (filePath, operation, parameters = {}, name = null) => {
  return {
    filePath,
    operation,
    parameters,
    name: name || operation,
    supportsAudio: supportsAudioPreservation(operation),
    isVideo: isVideoFile(filePath)
  }
}

/**
 * 验证视频处理参数
 * @param {string} operation - 操作类型
 * @param {Object} parameters - 参数
 * @returns {Object} 验证结果 {valid: boolean, errors: string[]}
 */
export const validateVideoParameters = (operation, parameters) => {
  const errors = []
  
  switch (operation) {
    case 'resize':
      if (parameters.scale_mode === 'custom') {
        if (!parameters.width && !parameters.height) {
          errors.push('Resize operation requires width or height')
        }
      } else if (parameters.scale_mode === 'ratio') {
        if (!parameters.scale_ratio || parameters.scale_ratio <= 0) {
          errors.push('Scale ratio must be greater than 0')
        }
      }
      break
      
    case 'binary':
      if (parameters.threshold && (parameters.threshold < 0 || parameters.threshold > 255)) {
        errors.push('Threshold must be between 0 and 255')
      }
      break
      
    case 'blur':
      if (parameters.kernel_size && parameters.kernel_size % 2 === 0) {
        errors.push('Kernel size must be odd')
      }
      break
      
    case 'extract_frame':
      if (parameters.frame_number && parameters.frame_number < 0) {
        errors.push('Frame number must be non-negative')
      }
      break
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}
