# 文件处理器项目

一个基于Flask + React + Celery的文件处理应用，支持图像和视频的各种处理操作。

## 🚀 特性

- **图像处理**: 锐化、伽马校正、灰度转换、边缘检测、图像融合等
- **视频处理**: 格式转换、滤镜应用、帧提取等
- **批量处理**: 支持多文件同时处理
- **实时进度**: WebSocket实时显示处理进度
- **现代化界面**: React + Vite构建的响应式前端
- **异步处理**: Celery分布式任务队列

## 📁 项目结构

```
├── backend/              # Flask后端API
│   ├── app.py           # 主应用入口
│   ├── routes/          # API路由
│   ├── tasks/           # Celery任务
│   └── utils/           # 工具函数
├── frontend/            # React前端
│   ├── src/             # 源代码
│   ├── public/          # 静态资源
│   └── dist/            # 构建输出
├── electron-app/        # Electron桌面应用
├── docs/                # 项目文档
└── docker-compose.yml   # 生产环境配置
```

## 🛠️ 开发环境设置

### 快速开始

本项目采用混合开发模式，基础服务使用Docker，应用服务在本地运行以支持IDE调试。

**详细设置指南**: 请参考 [LOCAL_DEVELOPMENT_SETUP.md](./LOCAL_DEVELOPMENT_SETUP.md)

### 简化步骤

1. **启动Redis服务**:
   ```bash
   # 如果系统有Redis
   redis-cli ping
   
   # 或使用Docker
   docker compose -f docker-compose.dev.yml --profile redis up -d
   ```

2. **启动后端**:
   ```bash
   cd backend
   python3 -m venv .venv
   source .venv/bin/activate
   pip install -r requirements.txt
   python app.py
   ```

3. **启动前端**:
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

4. **访问应用**: http://localhost:3000

## 🐳 生产部署

使用Docker Compose进行生产部署：

```bash
# 启动所有服务
docker compose up -d

# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f
```

服务访问：
- 前端: http://localhost:3000
- 后端API: http://localhost:5000/api
- Flower监控: http://localhost:5555

## 🔧 配置说明

### 环境变量

- `FLASK_ENV`: Flask运行环境 (development/production)
- `CELERY_BROKER_URL`: Celery消息队列URL
- `CELERY_RESULT_BACKEND`: Celery结果存储URL
- `REDIS_URL`: Redis连接URL

### 文件存储

- `backend/uploads/`: 上传文件存储
- `backend/output/`: 处理结果存储
- `backend/logs/`: 应用日志

## 🧪 测试

### 后端测试
```bash
cd backend
source .venv/bin/activate
python -m pytest
```

### 前端测试
```bash
cd frontend
npm test
```

## 📚 API文档

### 主要端点

- `GET /api/health` - 健康检查
- `POST /api/upload` - 文件上传
- `POST /api/process` - 批量处理
- `GET /api/operations` - 获取可用操作
- `GET /api/download/{filename}` - 下载处理结果

### 支持的操作

**图像处理**:
- `sharpen` - 图像锐化
- `gamma_correction` - 伽马校正
- `grayscale` - 灰度转换
- `edge_detection` - 边缘检测
- `image_fusion` - 图像融合

**视频处理**:
- `video_grayscale` - 视频灰度
- `video_resize` - 视频缩放
- `video_blur` - 视频模糊
- `video_edge_detection` - 视频边缘检测

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 故障排除

常见问题和解决方案请参考 [LOCAL_DEVELOPMENT_SETUP.md](./LOCAL_DEVELOPMENT_SETUP.md#故障排除) 中的故障排除部分。

## 📞 支持

如有问题或建议，请：
1. 查看 [Issues](../../issues) 页面
2. 创建新的 Issue
3. 参考文档和故障排除指南
