#!/usr/bin/env python3
"""
测试修改后的状态查询端点功能
"""
import asyncio
import json
from playwright.async_api import async_playwright

async def test_status_endpoint_change():
    """测试前端使用新的状态查询端点"""
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        # 监听网络请求
        requests = []
        
        def handle_request(request):
            if '/api/process/' in request.url:
                requests.append({
                    'url': request.url,
                    'method': request.method,
                    'post_data': request.post_data
                })
                print(f"API请求: {request.method} {request.url}")
        
        page.on('request', handle_request)
        
        try:
            # 访问前端应用
            print("访问前端应用...")
            await page.goto('http://localhost:3000')
            
            # 等待页面加载
            await page.wait_for_load_state('networkidle')
            
            # 检查连接状态
            status_element = await page.wait_for_selector('.status-bar', timeout=10000)
            status_text = await status_element.text_content()
            print(f"连接状态: {status_text}")
            
            # 如果连接失败，尝试点击状态栏更新API地址
            if '连接失败' in status_text or '未连接' in status_text:
                print("连接失败，尝试更新API地址...")
                await status_element.click()
                
                # 等待输入框出现
                input_element = await page.wait_for_selector('input[type="text"]', timeout=5000)
                await input_element.fill('http://localhost:5000')
                await page.keyboard.press('Enter')
                
                # 等待连接状态更新
                await page.wait_for_timeout(2000)
            
            # 检查是否有文件上传区域
            upload_area = await page.query_selector('.upload-area')
            if upload_area:
                print("找到文件上传区域")
                
                # 创建一个测试图片文件
                import tempfile
                import os
                from PIL import Image
                
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
                    # 创建一个简单的测试图片
                    img = Image.new('RGB', (100, 100), color='red')
                    img.save(tmp_file.name, 'JPEG')
                    test_file_path = tmp_file.name
                
                try:
                    # 上传文件
                    print(f"上传测试文件: {test_file_path}")
                    file_input = await page.query_selector('input[type="file"]')
                    if file_input:
                        await file_input.set_input_files(test_file_path)
                        
                        # 等待文件上传完成
                        await page.wait_for_timeout(2000)
                        
                        # 选择操作（如果有操作选择器）
                        operation_select = await page.query_selector('select')
                        if operation_select:
                            await operation_select.select_option('sharpen_image')
                            print("选择了图像锐化操作")
                        
                        # 点击处理按钮
                        process_button = await page.query_selector('button:has-text("开始处理")')
                        if not process_button:
                            process_button = await page.query_selector('button:has-text("处理")')
                        if not process_button:
                            process_button = await page.query_selector('button[type="submit"]')
                        
                        if process_button:
                            print("点击处理按钮...")
                            await process_button.click()
                            
                            # 等待处理开始
                            await page.wait_for_timeout(3000)
                            
                            # 检查是否有结果显示
                            results_area = await page.query_selector('.batch-results')
                            if results_area:
                                print("找到结果显示区域")
                                
                                # 等待一段时间让任务处理
                                await page.wait_for_timeout(10000)
                                
                                # 检查网络请求
                                print("\n=== 网络请求分析 ===")
                                for req in requests:
                                    print(f"请求: {req['method']} {req['url']}")
                                    if req['post_data']:
                                        try:
                                            data = json.loads(req['post_data'])
                                            print(f"  数据: {data}")
                                        except:
                                            print(f"  数据: {req['post_data']}")
                                
                                # 检查是否使用了正确的端点
                                status_requests = [r for r in requests if '/api/process/status' in r['url']]
                                results_requests = [r for r in requests if '/api/process/results' in r['url']]
                                
                                print(f"\n=== 端点使用分析 ===")
                                print(f"使用 /api/process/status 的请求: {len(status_requests)}")
                                print(f"使用 /api/process/results 的请求: {len(results_requests)}")
                                
                                if len(status_requests) > 0:
                                    print("✅ 前端正确使用了 /api/process/status 端点")
                                else:
                                    print("❌ 前端没有使用 /api/process/status 端点")
                                
                                if len(results_requests) > 0:
                                    print("⚠️  前端仍在使用 /api/process/results 端点")
                                else:
                                    print("✅ 前端没有使用旧的 /api/process/results 端点")
                            
                        else:
                            print("未找到处理按钮")
                    else:
                        print("未找到文件输入元素")
                        
                finally:
                    # 清理测试文件
                    if os.path.exists(test_file_path):
                        os.unlink(test_file_path)
            else:
                print("未找到文件上传区域")
            
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            # 关闭浏览器
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_status_endpoint_change())
