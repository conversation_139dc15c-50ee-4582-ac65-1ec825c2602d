"""
纹理迁移处理任务
"""
import cv2
import numpy as np
from celery import current_task
import os
import sys
import logging
import time
import random

logger = logging.getLogger(__name__)


def texture_transfer_task(self, target_image_path, source_texture_path, parameters):
    """简化的纹理迁移处理"""
    try:
        self.update_state(state='PROGRESS',
                         meta={'current': 5, 'total': 100,
                               'status': 'Loading images...'})

        # 读取目标图像和纹理源图像
        target_image = cv2.imread(target_image_path)
        source_texture = cv2.imread(source_texture_path)

        if target_image is None:
            raise ValueError(f"Cannot load target image from {target_image_path}")
        if source_texture is None:
            raise ValueError(f"Cannot load source texture from {source_texture_path}")

        # 获取参数并确保类型正确
        block_size = int(parameters.get('block_size', 32))
        alpha = float(parameters.get('alpha', 0.7))  # 纹理强度
        
        logger.info(f"Texture transfer: block_size={block_size}, alpha={alpha}")

        self.update_state(state='PROGRESS',
                         meta={'current': 20, 'total': 100,
                               'status': 'Resizing images...'})

        # 调整图像尺寸
        h, w = target_image.shape[:2]
        source_texture = cv2.resize(source_texture, (w, h))

        self.update_state(state='PROGRESS',
                         meta={'current': 40, 'total': 100,
                               'status': 'Processing texture transfer...'})

        # 简化的纹理迁移：基于块的纹理替换
        result = simple_texture_transfer(target_image, source_texture, block_size, alpha)

        self.update_state(state='PROGRESS',
                         meta={'current': 80, 'total': 100,
                               'status': 'Saving result...'})

        # 保存结果
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = int(time.time() * 1000)
        base_name = os.path.splitext(os.path.basename(target_image_path))[0]
        ext = os.path.splitext(os.path.basename(target_image_path))[1]
        output_filename = f'texture_transfer_{base_name}_{timestamp}{ext}'
        output_path = os.path.join(output_dir, output_filename)
        cv2.imwrite(output_path, result)

        logger.info(f"Texture transfer completed")

        return {
            'status': 'success',
            'output_path': output_path,
            'output_filename': output_filename,
            'target_image_path': target_image_path,
            'source_texture_path': source_texture_path,
            'block_size': block_size,
            'alpha': alpha
        }

    except Exception as e:
        logger.error(f"Texture transfer error: {str(e)}")
        self.update_state(state='FAILURE',
                         meta={'error': str(e)})
        raise


def simple_texture_transfer(target_image, source_texture, block_size, alpha):
    """简化的纹理迁移算法"""
    h, w = target_image.shape[:2]
    result = target_image.copy().astype(np.float32)
    
    # 将源纹理转换为灰度用于匹配
    target_gray = cv2.cvtColor(target_image, cv2.COLOR_BGR2GRAY)
    source_gray = cv2.cvtColor(source_texture, cv2.COLOR_BGR2GRAY)
    
    # 提取源纹理的所有块
    texture_blocks = []
    for y in range(0, source_texture.shape[0] - block_size + 1, block_size // 2):
        for x in range(0, source_texture.shape[1] - block_size + 1, block_size // 2):
            block = source_texture[y:y+block_size, x:x+block_size]
            gray_block = source_gray[y:y+block_size, x:x+block_size]
            texture_blocks.append((block, gray_block))
    
    # 对目标图像的每个块进行纹理替换
    for y in range(0, h - block_size + 1, block_size):
        for x in range(0, w - block_size + 1, block_size):
            # 获取目标块
            target_block = target_image[y:y+block_size, x:x+block_size]
            target_gray_block = target_gray[y:y+block_size, x:x+block_size]
            
            # 找到最匹配的纹理块
            best_block = find_best_texture_block(target_gray_block, texture_blocks)
            
            if best_block is not None:
                # 混合纹理
                mixed_block = (1 - alpha) * target_block.astype(np.float32) + alpha * best_block.astype(np.float32)
                result[y:y+block_size, x:x+block_size] = mixed_block
    
    return np.clip(result, 0, 255).astype(np.uint8)


def find_best_texture_block(target_gray_block, texture_blocks):
    """找到最匹配的纹理块"""
    if not texture_blocks:
        return None
    
    best_block = None
    min_error = float('inf')
    
    # 随机采样一些块来提高速度
    sample_size = min(50, len(texture_blocks))
    sampled_blocks = random.sample(texture_blocks, sample_size)
    
    for texture_block, texture_gray_block in sampled_blocks:
        # 计算灰度差异
        if texture_gray_block.shape == target_gray_block.shape:
            error = np.mean((texture_gray_block.astype(np.float32) - target_gray_block.astype(np.float32)) ** 2)
            if error < min_error:
                min_error = error
                best_block = texture_block
    
    return best_block