{"permissions": {"allow": ["mcp__zen__analyze", "mcp__feedback-enhanced__interactive_feedback", "mcp__zen__codereview", "<PERSON><PERSON>(python:*)", "Bash(sudo systemctl start:*)", "Bash(sudo systemctl status:*)", "<PERSON>sh(redis-server:*)", "Bash(redis-cli:*)", "<PERSON><PERSON>(uv venv:*)", "<PERSON><PERSON>(source:*)", "Bash(uv pip install:*)", "<PERSON><PERSON>(docker run:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_click", "mcp__playwright__browser_press_key", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_close", "mcp__zen__debug", "Bash(pip3 install:*)", "Bash(pip install:*)", "Bash(ls:*)", "<PERSON><PERSON>(pip uninstall:*)", "Bash(.venv/bin/pip uninstall:*)", "Bash(.venv/bin/pip install:*)", "Bash(rm:*)", "Bash(/home/<USER>/projects/homework/backend/.venv/bin/python test_task.py)", "Bash(/home/<USER>/projects/homework/backend/.venv/bin/python test_registered_tasks.py)", "Bash(/home/<USER>/projects/homework/backend/.venv/bin/python test_batch_processing.py)", "Bash(grep:*)", "<PERSON><PERSON>(pkill:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(find:*)", "mcp__playwright__browser_select_option", "mcp__playwright__browser_file_upload", "Bash(kill:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["mcp-feedback-enhanced"]}