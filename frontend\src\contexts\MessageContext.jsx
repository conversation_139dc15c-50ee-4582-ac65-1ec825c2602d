import React, { createContext, useContext, useState, useCallback } from 'react'

const MessageContext = createContext()

export const useMessage = () => {
  const context = useContext(MessageContext)
  if (!context) {
    throw new Error('useMessage must be used within a MessageProvider')
  }
  return context
}

export const MessageProvider = ({ children }) => {
  const [messages, setMessages] = useState([])

  // 添加消息
  const addMessage = useCallback((text, type = 'info', duration = 5000) => {
    const id = Date.now() + Math.random()
    const message = {
      id,
      text,
      type,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, message])

    // 自动移除消息
    if (duration > 0) {
      setTimeout(() => {
        removeMessage(id)
      }, duration)
    }

    return id
  }, [])

  // 移除消息
  const removeMessage = useCallback((id) => {
    setMessages(prev => prev.filter(msg => msg.id !== id))
  }, [])

  // 清除所有消息
  const clearMessages = useCallback(() => {
    setMessages([])
  }, [])

  // 便捷方法
  const showSuccess = useCallback((text, duration) => {
    return addMessage(text, 'success', duration)
  }, [addMessage])

  const showError = useCallback((text, duration) => {
    return addMessage(text, 'error', duration)
  }, [addMessage])

  const showInfo = useCallback((text, duration) => {
    return addMessage(text, 'info', duration)
  }, [addMessage])

  const showWarning = useCallback((text, duration) => {
    return addMessage(text, 'warning', duration)
  }, [addMessage])

  const value = {
    messages,
    addMessage,
    removeMessage,
    clearMessages,
    showSuccess,
    showError,
    showInfo,
    showWarning
  }

  return (
    <MessageContext.Provider value={value}>
      {children}
    </MessageContext.Provider>
  )
}
