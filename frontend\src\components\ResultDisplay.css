.result-container {
  animation: fadeIn 0.5s ease;
}

.result-container.success {
  border-left: 4px solid #10b981;
}

.result-container.error {
  border-left: 4px solid #ef4444;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
}

.result-icon {
  width: 24px;
  height: 24px;
}

.success-icon {
  color: #10b981;
}

.error-icon {
  color: #ef4444;
}

.result-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.result-message {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
}

.result-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .result-header {
    gap: 10px;
  }
  
  .result-title {
    font-size: 16px;
  }
  
  .result-message {
    font-size: 13px;
  }
  
  .result-actions {
    flex-direction: column;
  }
}
