"""
图像伽马校正任务
"""
import cv2
import numpy as np
import os
import sys
import time
import logging
from celery import current_task

logger = logging.getLogger(__name__)


def safe_float(value, default=0.0):
    """安全地将值转换为浮点数"""
    if isinstance(value, (int, float)):
        return float(value)
    if isinstance(value, str):
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    return default


def gamma_correction_task(self, file_path, parameters):
    """伽马校正处理"""
    try:
        self.update_state(state='PROGRESS',
                         meta={'current': 10, 'total': 100,
                               'status': 'Loading image...'})

        image = cv2.imread(file_path)
        if image is None:
            raise ValueError(f"Cannot load image from {file_path}")

        self.update_state(state='PROGRESS',
                         meta={'current': 40, 'total': 100,
                               'status': 'Applying gamma correction...'})

        gamma = safe_float(parameters.get('gamma', 1.0), 1.0)

        # 构建查找表
        inv_gamma = 1.0 / gamma
        table = np.array([((i / 255.0) ** inv_gamma) * 255
                         for i in np.arange(0, 256)]).astype("uint8")

        # 应用伽马校正
        corrected = cv2.LUT(image, table)

        self.update_state(state='PROGRESS',
                         meta={'current': 80, 'total': 100,
                               'status': 'Saving result...'})

        # 保存结果
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        # 生成唯一文件名避免缓存问题 - 使用任务ID确保唯一性
        task_id = self.request.id if hasattr(self, 'request') and self.request else 'unknown'
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        ext = os.path.splitext(os.path.basename(file_path))[1]
        output_filename = f'gamma_{base_name}_{task_id}{ext}'
        output_path = os.path.join(output_dir, output_filename)
        cv2.imwrite(output_path, corrected)

        result = {
            'status': 'success',
            'output_path': output_path,
            'output_filename': output_filename,
            'original_path': file_path,
            'gamma_applied': gamma
        }

        # 设置任务状态为成功
        self.update_state(state='SUCCESS', meta=result)
        return result

    except Exception as e:
        error_msg = str(e)
        logger.error(f"Gamma correction task failed: {error_msg}")
        raise Exception(f"Gamma correction failed: {error_msg}")