"""
视频二值化处理任务
"""
import os
import logging
from celery import current_task

from tasks.opencv_ffmpeg_processor import OpenCVFFmpegProcessor, create_opencv_processor

logger = logging.getLogger(__name__)


def create_celery_progress_callback(task_instance):
    """创建Celery进度回调函数"""
    def progress_callback(current, total, status="Processing"):
        task_instance.update_state(
            state='PROGRESS',
            meta={'current': current, 'total': total, 'status': status}
        )
    return progress_callback


def binary_video_task(self, file_path, parameters):
    """视频二值化处理"""
    try:
        progress_callback = create_celery_progress_callback(self)
        
        # 获取二值化参数
        threshold = parameters.get('threshold', 127)
        max_value = parameters.get('max_value', 255)
        threshold_type = parameters.get('threshold_type', 'binary')
        
        # 创建二值化处理器
        opencv_processor = create_opencv_processor(
            'binary', 
            threshold=threshold, 
            max_value=max_value, 
            threshold_type=threshold_type
        )
        
        with OpenCVFFmpegProcessor(file_path, preserve_audio=True) as processor:
            result = processor.process_frames_with_opencv(
                opencv_processor=opencv_processor,
                output_prefix='binary',
                progress_callback=progress_callback
            )
            
            # 添加二值化参数到结果
            result.update({
                'threshold': threshold,
                'threshold_type': threshold_type,
                'max_value': max_value
            })
            
            self.update_state(state='SUCCESS', meta=result)
            return result
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Binary video task failed: {error_msg}")
        self.update_state(state='FAILURE', meta={'error': error_msg})
        raise Exception(f"Video binary processing failed: {error_msg}")