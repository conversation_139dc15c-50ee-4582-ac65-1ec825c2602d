# 本地开发环境设置指南

## 概述

本项目已重构为混合开发模式：
- **基础服务**：Redis 通过 Docker 容器运行（可选，也可使用系统Redis）
- **应用服务**：前端和后端在本地运行，支持IDE调试和热重载

## 系统要求

### 必需软件
- **Python 3.12+** - 后端开发
- **Node.js 18+** - 前端开发
- **Docker** - 基础服务（可选）
- **Redis** - 消息队列（系统安装或Docker）

### 系统依赖（后端）
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3-dev python3-venv
sudo apt install -y libopencv-dev ffmpeg

# macOS
brew install opencv ffmpeg

# Windows
# 请参考OpenCV和FFmpeg官方安装指南
```

## 快速开始

### 1. 启动Redis服务

**选项A：使用系统Redis（推荐）**
```bash
# 检查Redis是否运行
redis-cli ping
# 如果返回PONG，则Redis已运行
```

**选项B：使用Docker Redis**
```bash
# 启动Redis容器（如果系统没有Redis）
docker compose -f docker-compose.dev.yml --profile redis up -d
```

### 2. 设置后端环境

```bash
cd backend

# 创建虚拟环境
python3 -m venv .venv

# 激活虚拟环境
source .venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 如果遇到NumPy版本冲突，执行：
pip install "numpy<2"
pip install opencv-python==********

# 测试Redis连接
python3 -c "import redis; r = redis.Redis(host='localhost', port=6379, db=0); print('Redis connection:', r.ping())"

# 测试Flask应用
python3 -c "from app import app; print('Flask app ready')"
```

### 3. 设置前端环境

```bash
cd frontend

# 安装依赖（如果尚未安装）
npm install

# 启动开发服务器
npm run dev
```

## 开发工作流

### 启动开发环境

1. **启动Redis**（如果使用Docker）：
   ```bash
   docker compose -f docker-compose.dev.yml --profile redis up -d
   ```

2. **启动后端**：
   ```bash
   cd backend
   source .venv/bin/activate
   python app.py
   ```

3. **启动Celery Worker**（新终端）：
   ```bash
   cd backend
   source .venv/bin/activate
   celery -A app.celery worker --loglevel=info
   ```

4. **启动前端**（新终端）：
   ```bash
   cd frontend
   npm run dev
   ```

### 访问应用

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:5000/api
- **API健康检查**: http://localhost:5000/api/health

## IDE调试配置

### VS Code 配置

**后端调试** (`.vscode/launch.json`):
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Flask",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/backend/app.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}/backend",
            "env": {
                "FLASK_ENV": "development",
                "FLASK_DEBUG": "true"
            },
            "python": "${workspaceFolder}/backend/.venv/bin/python"
        }
    ]
}
```

**前端调试**：
- 使用浏览器开发者工具
- VS Code可以通过Chrome调试扩展连接

## 故障排除

### 常见问题

**1. Redis连接失败**
```bash
# 检查Redis状态
redis-cli ping
# 或检查进程
ps aux | grep redis
```

**2. NumPy版本冲突**
```bash
pip install "numpy<2"
pip install opencv-python==********
```

**3. 端口占用**
```bash
# 检查端口占用
lsof -i :3000  # 前端
lsof -i :5000  # 后端
lsof -i :6379  # Redis
```

**4. 虚拟环境问题**
```bash
# 重新创建虚拟环境
rm -rf backend/.venv
cd backend
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

### 环境变量

后端默认配置（`backend/config.py`）：
- `CELERY_BROKER_URL`: redis://localhost:6379/0
- `CELERY_RESULT_BACKEND`: redis://localhost:6379/0

如需修改，可设置环境变量：
```bash
export CELERY_BROKER_URL=redis://localhost:6380/0
export CELERY_RESULT_BACKEND=redis://localhost:6380/0
```

## 生产部署

生产环境仍可使用完整的Docker配置：
```bash
docker compose up -d
```

这将启动所有服务的容器化版本，适用于生产部署。
