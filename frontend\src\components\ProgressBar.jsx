import React from 'react'
import './ProgressBar.css'

const ProgressBar = ({
  progress = 0,
  status = 'processing',
  showPercentage = true,
  size = 'medium',
  animated = true,
  label = null,
  showTitle = false
}) => {
  // 确保进度值在0-100之间
  const normalizedProgress = Math.max(0, Math.min(100, progress))

  // 根据状态确定颜色类
  const getStatusClass = () => {
    switch (status) {
      case 'success':
      case 'completed':
        return 'progress-success'
      case 'error':
      case 'failed':
        return 'progress-error'
      case 'warning':
        return 'progress-warning'
      case 'processing':
      case 'pending':
      default:
        return 'progress-processing'
    }
  }

  return (
    <div className={`progress-container ${showTitle ? 'card' : ''} ${size}`}>
      {showTitle && (
        <h3 className="progress-title">处理进度</h3>
      )}

      {label && (
        <div className="progress-label">
          {label}
        </div>
      )}

      <div className="progress-bar-wrapper">
        <div className={`progress-bar ${getStatusClass()}`}>
          <div
            className={`progress-fill ${animated ? 'animated' : ''}`}
            style={{ width: `${normalizedProgress}%` }}
          >
            {animated && (
              <div className="progress-shine"></div>
            )}
          </div>
        </div>

        {showPercentage && (
          <div className="progress-percentage">
            {Math.round(normalizedProgress)}%
          </div>
        )}
      </div>

      {showTitle && (
        <div className="progress-text">
          {Math.round(normalizedProgress)}% 完成
        </div>
      )}
    </div>
  )
}

export default ProgressBar
