version: '3.8'

services:
  # Redis 服务 - Celery 消息队列
  redis:
    image: redis:7-alpine
    container_name: file-processor-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - file-processor-network

  # 后端 Flask API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: file-processor-backend
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/output:/app/output
      - ./backend/logs:/app/logs
    depends_on:
      - redis
    networks:
      - file-processor-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker 服务
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: file-processor-worker
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=info --concurrency=4
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/output:/app/output
      - ./backend/logs:/app/logs
    depends_on:
      - redis
      - backend
    networks:
      - file-processor-network

  # 前端 React 应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: file-processor-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:5000/api
    depends_on:
      - backend
    networks:
      - file-processor-network

volumes:
  redis_data:
    driver: local

networks:
  file-processor-network:
    driver: bridge
