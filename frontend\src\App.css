.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.app-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 30px;
  align-items: start;
  min-height: 600px;
}

.tab-navigation {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 10px;
}

.tab-button {
  padding: 10px 20px;
  border: none;
  background: #f8f9fa;
  color: #666;
  border-radius: 4px 4px 0 0;
  cursor: pointer;
  font-size: 1em;
  transition: all 0.3s;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  background: #e9ecef;
  color: #333;
}

.tab-button.active {
  background: white;
  color: #007bff;
  border-bottom-color: #007bff;
  font-weight: 500;
}

.processor-section {
  display: flex;
  flex-direction: column;
}

.log-section {
  display: flex;
  flex-direction: column;
  height: 600px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .log-section {
    height: 400px;
  }

  .container {
    padding: 0 15px;
  }

  .main-content {
    padding: 15px 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 10px;
  }
  
  .grid {
    gap: 15px;
  }
}
